﻿using FloraPack.API.Reports.Boekestyns;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Boekestyn.Entities;
using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Spire;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Customer = FloraPack.API.Repositories.Boekestyn.Entities.Customer;

namespace FloraPack.API.Controllers;

[Route("API/Boekestyns")]
public class BoekestynController(IConfiguration configuration, IHubContext<FloraPackHub> hubContext,
        BoekestynRepository boekestynRepository, SpireRepository spireRepository, CouchRepository couchRepository,
        PrebookRepository prebookRepository, FutureOrderRepository futureOrderRepository)
    : FloraPackControllerBase(configuration)
{
    [HttpGet("Items")]
    public async Task<IActionResult> ItemList([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var items = await boekestynRepository.BoekestynItems(startDate, endDate);

        var response = new ItemListResponse(items);

        return Ok(response);
    }

    [HttpPost("Items/Download")]
    public IActionResult ItemsListDownload([FromBody] ItemListReportModel model)
    {
        var report = BoekestynReportFactory.ItemList(model);

        Response.Headers.Append("Content-Disposition", $"attachment;filename=BoekestynItems.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Items/Requirements")]
    public async Task<IActionResult> ItemListRequirements([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var items = await boekestynRepository.ItemListRequirements(startDate, endDate);

        var response = new ItemListRequirementsResponse(items);
        return Ok(response);
    }

    [HttpPost("Sales/Download")]
    public IActionResult SalesDownload([FromBody] SalesReportModel model)
    {
        var report = BoekestynReportFactory.Sales(model);

        Response.Headers.Append("Content-Disposition", $"attachment;filename=BoekestynSales.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Plants")]
    public async Task<IActionResult> Plants()
    {
        var plants = await couchRepository.GetPlants();
        var response = new PlantsResponse(plants);
        return Ok(response);
    }

    [HttpGet("Customers")]
    public async Task<IActionResult> Customers()
    {
        var customers = await couchRepository.GetCustomers();
        var response = new CustomersResponse(customers);
        return Ok(response);
    }

    [HttpGet("ProductionOrders")]
    public async Task<IActionResult> ProductionOrders([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var productionOrders = await couchRepository.GetProductionOrders(startDate, endDate);
        var zones = await couchRepository.GetZones();

        var offsiteZones = zones
            .Where(z => z.IsOffsite)
            .Select(z => z.Id)
            .ToList();

        var finishingProductionOrders = productionOrders
            .Where(o => !offsiteZones.Contains(o.FullSpaceZone?.Id ?? string.Empty))
            .ToList();

        var response = new ProductionOrderResponse(finishingProductionOrders);
        return Ok(response);
    }

    [HttpGet("Sales")]
    public async Task<IActionResult> Sales([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var plants = (await couchRepository.GetPlants()).OrderBy(p => p.Name).ToList();
        var prebookItems = await boekestynRepository.GetPrebookItems(startDate, endDate);
        var productionOrders = await couchRepository.GetProductionOrders(startDate, endDate);

        var zones = await couchRepository.GetZones();

        var offsiteZones = zones
            .Where(z => z.IsOffsite)
            .Select(z => z.Id)
            .ToList();

        var finishingProductionOrders = productionOrders
            .Where(o => !offsiteZones.Contains(o.FullSpaceZone?.Id ?? string.Empty))
            .ToList();

        var response = new SalesResponse(plants, prebookItems, finishingProductionOrders);
        
        return Ok(response);
    }

    [HttpGet("PurchaseOrders")]
    public async Task<IActionResult> BoekestynPurchaseOrderTasks([FromQuery] DateTime? start = null, [FromQuery] DateTime? end = null)
    {
        var orders = await spireRepository.BoekestynPurchaseOrderTaskList(start, end);
        
        var response = new BoekestynPurchaseOrderTaskResponse(orders);

        return Ok(response);
    }

    [HttpGet("PurchaseOrders/{id:int}/Url", Name = nameof(BoekestynPurchaseOrderUrl))]
    public async Task<IActionResult> BoekestynPurchaseOrderUrl(int id)
    {
        var bytes = await spireRepository.BoekestynPurchaseOrderReportData(id);
        if (bytes == null) {
            return NotFound();
        }

        var data = Convert.ToBase64String(bytes);
        var response = new BoekestynPurchaseOrderUrlResponse($"data:application/pdf;base64,{data}");
        return Ok(response);
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/Priority")]
    public async Task<IActionResult> PurchaseOrderItemPriority(int id, [FromQuery] bool priority)
    {
        await spireRepository.SetPurchaseOrderItemPriority(id, priority);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);

        return Ok();
    }

    [HttpPut("Prebooks/Items/{id:int}/Priority")]
    public async Task<IActionResult> PrebookItemPriority(int id, [FromQuery] bool priority)
    {
        await prebookRepository.SetPrebookItemPriority(id, priority);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);

        return Ok();
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/UpcComplete")]
    public async Task<IActionResult> PurchaseOrderItemUpcComplete(int id)
    {
        await spireRepository.SetPurchaseOrderItemUpcComplete(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);
        return Ok();
    }

    [HttpPut("Prebooks/Items/{id:int}/UpcComplete")]
    public async Task<IActionResult> PrebookItemUpcComplete(int id)
    {
        await prebookRepository.SetPrebookItemUpcComplete(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);
        return Ok();
    }

    [HttpPut("Prebooks/Items/{id:int}/UpcUncomplete")]
    public async Task<IActionResult> PrebookItemUpcUncomplete(int id)
    {
        await prebookRepository.SetPrebookItemUpcUncomplete(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);
        return Ok();
    }

    [HttpPost("Prebooks/Items/{id:int}/UpcOverride")]
    public async Task<IActionResult> PrebookItemUpcOverride(int id, [FromBody] PrebookItemUpcOverrideModel model)
    {
        var items = await prebookRepository.ItemDetailForFutureOrderItemId(id);
        var firstItem = items.FirstOrDefault();

        if (firstItem == null) {
            return NotFound();
        }

        var cc = model.Cc
            .Split(';')
            .Where(cc => !string.IsNullOrWhiteSpace(cc))
            .Select(c => c.Trim())
            .ToList();
        await SendMail(model.Subject, model.Body, ["<EMAIL>"], cc);

        await prebookRepository.SetPrebookItemUpcOverride(firstItem.Id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, firstItem.Id);
        return Ok();
    }

    [HttpPost("FutureOrders/{id:int}/UpcOverride")]
    public async Task<IActionResult> FutureOrderUpcOverride(int id,
        [FromBody] FutureOrderUpcOverrideModel model)
    {
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return NotFound();
        }

        var futureOrderItemIds = futureOrder.Items.Select(i => i.Id).ToList();
        var prebooks = await prebookRepository.DetailsForFutureOrder(id);
        var printedPrebookItemIds = prebooks.Aggregate(new List<int>(), (list, prebook) => {
            list.AddRange(prebook.Items.Where(i => futureOrderItemIds.Contains(i.FutureOrderItemId.GetValueOrDefault()))
                .Select(i => i.Id));
            return list;
        });

        var cc = model.Cc
            .Split(';')
            .Where(cc => !string.IsNullOrWhiteSpace(cc))
            .Select(c => c.Trim())
            .ToList();
        await SendMail(model.Subject, model.Body, ["<EMAIL>"], cc);

        await prebookRepository.SetPrebookItemUpcOverrideMultiple(printedPrebookItemIds);
        foreach (var prebookItemId in printedPrebookItemIds) {
            await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, prebookItemId);
        }

        return Ok();
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/PrepStart")]
    public async Task<IActionResult> PrepStart(int id)
    {
        await spireRepository.SetPurchaseOrderItemPrepStart(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);
        return Ok();
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/PrepComplete")]
    public async Task<IActionResult> PrepComplete(int id)
    {
        await spireRepository.SetPurchaseOrderItemPrepComplete(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);
        return Ok();
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/PackingStart")]
    public async Task<IActionResult> PackingStart(int id)
    {
        await spireRepository.SetPurchaseOrderItemPackingStart(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);

        return Ok();
    }

    [HttpPut("PurchaseOrders/Items/{id:int}/PackingComplete")]
    public async Task<IActionResult> PackingComplete(int id)
    {
        await spireRepository.SetPurchaseOrderItemPackingComplete(id);
        await hubContext.Clients.All.SendAsync(FloraPackHub.BoekestynTaskUpdateMethod, id);

        var who = await UserName();
        var task = await spireRepository.BoekestynPurchaseOrderTask(id);
        if (task != null && task.Items.Count == 1) {
            await couchRepository.CreateDriverTask(task, task.Items.First(), who!);
        }

        return Ok();
    }

    private record ItemListResponse(IEnumerable<ItemListItem> Items);

    public class ItemListReportModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<ItemListItem> Items { get; set; } = [];
    }

    public class SalesReportModel
    {
        public string? Plant { get; set; }
        public List<string> Customers { get; set; } = [];
        public List<Plant> Plants { get; set; } = [];
        public List<BoekestynPrebookItem> PrebookItems { get; set; } = [];
        public List<BoekestynProductionOrder> ProductionOrders { get; set; } = [];
    }

    private record ItemListRequirementsResponse(IEnumerable<ItemListRequirementsItem> Items);

    private record PlantsResponse(IEnumerable<Plant> Plants);
    
    private record CustomersResponse(IEnumerable<Customer> Customers);

    private record ProductionOrderResponse(IEnumerable<BoekestynProductionOrder> Orders);

    private record SalesResponse(List<Plant> Plants, List<BoekestynPrebookItem> PrebookItems, List<BoekestynProductionOrder> ProductionOrders);

    private record BoekestynPurchaseOrderTaskResponse(IEnumerable<SpireRepository.TaskListPurchaseOrder> Tasks);

    private record BoekestynPurchaseOrderUrlResponse(string Url);

    public class PrebookItemUpcOverrideModel
    {
        public string Cc { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
    }

    public class FutureOrderUpcOverrideModel
    {
        public string Cc { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
    }

}