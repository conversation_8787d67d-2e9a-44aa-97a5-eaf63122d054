using System;
using System.Collections.Generic;
using System.Globalization;

namespace FloraPack.API.Reports
{
    public partial class HeinensLabels : GrapeCity.ActiveReports.SectionReport
    {
        public HeinensLabels(List<HeinensLabelItem> items)
        {
            InitializeComponent();

            DataSource = items;
        }

        private void OnReportStart(object sender, EventArgs e)
        {
            Document.Printer.PrinterName = string.Empty;
        }
    }

    public class HeinensLabelItem
    {
        public string StoreNumber { get; set; } = string.Empty;
        public string Destination { get; set; } = string.Empty;
        public string ProductNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UPC { get; set; } = string.Empty;
        public DateTime ReceivingDate { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int LabelNumber { get; set; }
        public int LabelCount { get; set; }

        public string DescriptionDisplay => $"{Description}\nPack {PackQuantity}";
        public string PurchaseOrderNumberDisplay => string.IsNullOrWhiteSpace(PurchaseOrderNumber) ? string.Empty : $"PO#:{PurchaseOrderNumber}";
        public string LabelNumberDisplay => LabelNumber == 0 ? "SKID" : $"{LabelNumber} of {LabelCount}";
        public string ProductNumberDisplay => $"Article# {ProductNumber}";
    }
}