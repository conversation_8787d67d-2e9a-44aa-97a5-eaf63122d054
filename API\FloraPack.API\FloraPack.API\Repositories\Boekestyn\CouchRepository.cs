﻿using System.Globalization;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using FloraPack.API.Repositories.Boekestyn.Couch;
using FloraPack.API.Repositories.Boekestyn.Entities;
using FloraPack.API.Spire;
using Customer = FloraPack.API.Repositories.Boekestyn.Entities.Customer;

namespace FloraPack.API.Repositories.Boekestyn;

public class CouchRepository(IHttpClientFactory httpClientFactory)
{
    private readonly HttpClient _httpClient = httpClientFactory.CreateClient("Couch");

    public async Task<List<BoekestynProductionOrder>> GetProductionOrders(DateTime? startDate, DateTime? endDate)
    {
        var startOfWeek = default(DateTime?);
        if (startDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(startDate.Value);
            var year = ISOWeek.GetYear(startDate.Value);
            startOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Monday);
        }

        var endOfWeek = default(DateTime?);
        if (endDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(endDate.Value);
            var year = ISOWeek.GetYear(endDate.Value);
            endOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Saturday);
        }

        var startQuery = startOfWeek.HasValue ? $"&startkey=\"{startOfWeek.Value:yyyy-MM-dd}\"" : string.Empty;
        var endQuery = endOfWeek.HasValue ? $"&endkey=\"{endOfWeek.Value:yyyy-MM-dd}\"" : string.Empty;
        var url = $"_design/filters/_view/orders-by-flower-date?include_docs=true{startQuery}{endQuery}";
        var response = await _httpClient.GetAsync(url);
        while (response.StatusCode == HttpStatusCode.TooManyRequests) {
            Thread.Sleep(TimeSpan.FromSeconds(3));
            response = await _httpClient.GetAsync(url);
        }

        var viewResponse = await response.Content.ReadFromJsonAsync<ViewResponse<BoekestynProductionOrder, object, string>>();
        var futureOrders = viewResponse?.Rows.Select(row => row.Doc).ToList();
        return futureOrders ?? new();
    }

    public async Task<List<StickingOrder>> GetStickingOrders(DateTime? startDate, DateTime? endDate)
    {
        var startQuery = startDate.HasValue ? $"&startkey=\"{startDate.Value:yyyy-MM-dd}\"" : string.Empty;
        var endQuery = endDate.HasValue ? $"&endkey=\"{endDate.Value:yyyy-MM-dd}\"" : string.Empty;
        var url = $"_design/filters/_view/orders-by-stick-date?include_docs=true{startQuery}{endQuery}";
        var response = await _httpClient.GetAsync(url);
        while (response.StatusCode == HttpStatusCode.TooManyRequests) {
            Thread.Sleep(TimeSpan.FromSeconds(3));
            response = await _httpClient.GetAsync(url);
        }

        var viewResponse = await response.Content.ReadFromJsonAsync<ViewResponse<StickingOrder, object, string>>();
        var orders = viewResponse?.Rows.Select(row => row.Doc).ToList();
        return orders ?? new();
    }

    public async Task<List<SpacingOrder>> GetSpacingOrders(DateTime? startDate, DateTime? endDate)
    {
        var startQuery = startDate.HasValue ? $"&startkey=\"{startDate.Value:yyyy-MM-dd}\"" : string.Empty;
        var endQuery = endDate.HasValue ? $"&endkey=\"{endDate.Value:yyyy-MM-dd}\"" : string.Empty;
        var url = $"_design/filters/_view/orders-by-space-date?include_docs=true{startQuery}{endQuery}";
        var response = await _httpClient.GetAsync(url);
        while (response.StatusCode == HttpStatusCode.TooManyRequests) {
            Thread.Sleep(TimeSpan.FromSeconds(3));
            response = await _httpClient.GetAsync(url);
        }

        var viewResponse = await response.Content.ReadFromJsonAsync<ViewResponse<SpacingOrder, object, string>>();
        var orders = viewResponse?.Rows.Select(row => row.Doc).ToList();
        return orders ?? new();
    }

    public async Task MarkOrderAsStickingScheduled(string id, bool stickingScheduled)
    {
        var response = await _httpClient.GetAsync(id);
        while (response.StatusCode == HttpStatusCode.TooManyRequests) {
            Thread.Sleep(TimeSpan.FromSeconds(3));
            response = await _httpClient.GetAsync(id);
        }

        if (!response.IsSuccessStatusCode) {
            var error = await response.Content.ReadAsStringAsync();
            throw new Exception($"Could not retrieve Boekestyn Order.\n\nError: {error}");
        }

        var content = await response.Content.ReadAsStringAsync();
        // try this: https://stackoverflow.com/a/71696424
        var order = JsonSerializer.Deserialize<JsonNode>(content);
        if (order == null) {
            throw new Exception("Could not deserialize Boekestyn Order.");
        }

        order["stickingScheduled"] = stickingScheduled;
        var json = JsonSerializer.Serialize(order);
        var putResponse = await _httpClient.PutAsync(id, new StringContent(json, Encoding.UTF8, "application/json"));

        while (putResponse.StatusCode == HttpStatusCode.TooManyRequests) {
            Thread.Sleep(TimeSpan.FromSeconds(3));
            putResponse = await _httpClient.PutAsync(id, new StringContent(json, Encoding.UTF8, "application/json"));
        }

        if (!putResponse.IsSuccessStatusCode) {
            throw new ApplicationException("Could not flag Boekestyn Order as Sticking Scheduled.");
        }
    }

    public async Task<List<Plant>> GetPlants()
    {
        var url = "_design/filters/_view/plants?include_docs=true";
        var plantResponse = await _httpClient.GetAsync(url);

        var plantResults = await plantResponse.Content.ReadFromJsonAsync<NullValueViewResponse<Plant>>();

        var plants = plantResults?.Rows.Select(r => r.Doc).ToList();

        return plants ?? new ();
    }

    public async Task<List<Zone>> GetZones()
    {
        var url = "_design/filters/_view/zones?include_docs=true";
        var response = await _httpClient.GetAsync(url);

        var results = await response.Content.ReadFromJsonAsync<NullValueViewResponse<Zone>>();

        var zones = results?.Rows.Select(r => r.Doc).ToList();

        return zones ?? new ();
    }

    public async Task<List<Customer>> GetCustomers()
    {
        var url = "_design/filters/_view/customers?include_docs=true";
        var customerResponse = await _httpClient.GetAsync(url);

        var customerResults = await customerResponse.Content.ReadFromJsonAsync<NullValueViewResponse<Customer>>();

        var customers = customerResults?.Rows.Select(r => r.Doc).ToList();

        return customers ?? new ();
    }

    public async Task CreateDriverTask(SpireRepository.TaskListPurchaseOrder order, SpireRepository.TaskListPurchaseOrderItem item, string who)
    {
        var url = "";
        var task = new DriverTaskCreate(order, item, who);
        var content = new StringContent(JsonSerializer.Serialize(task), Encoding.UTF8, "application/json");
        var response = await _httpClient.PostAsync(url, content);
        if (!response.IsSuccessStatusCode) {
            var error = await response.Content.ReadAsStringAsync();
            throw new Exception($"Failed to create driver task.\n\nError: {error}");
            
        }
    }
}