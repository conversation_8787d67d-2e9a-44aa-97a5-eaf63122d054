﻿using Dapper;
using FloraPack.API.Repositories.Boekestyn.Entities;
using System.Data;

namespace FloraPack.API.Repositories.Boekestyn;

public class StickingRepository(IConfiguration configuration) : BoekestynRepository(configuration)
{
    public async Task<IEnumerable<StickingLine>> GetStickingLines() =>
        await GetConnection().QueryAsync<StickingLine>("SELECT * FROM sticking_lines");

    public async Task<IEnumerable<StickingSchedule>> GetStickingSchedules(DateTime date)
    {
        var multi = await GetConnection().QueryMultipleAsync(@"
SELECT id, to_char(date, 'yyyy-MM-dd') date, line_id FROM sticking_schedules WHERE date = @date;
SELECT o.* FROM sticking_schedules s join sticking_work_orders o on s.id = o.schedule_id WHERE s.date = @date ORDER BY o.sort_order;
SELECT v.* FROM sticking_schedules s join sticking_work_orders o on s.id = o.schedule_id join sticking_work_order_varieties v on o.id = v.work_order_id WHERE s.date = @date;", new { date });

        var schedules = (await multi.ReadAsync<StickingSchedule>()).ToList();
        var orders = (await multi.ReadAsync<StickingWorkOrder>()).ToLookup(o => o.ScheduleId);
        var varieties = (await multi.ReadAsync<StickingWorkOrderVariety>()).ToLookup(v => v.WorkOrderId);

        foreach (var line in schedules.Where(l => orders.Contains(l.Id))) {
            var workOrders = orders[line.Id].ToList();
            foreach (var order in workOrders) {
                order.Varieties.AddRange(varieties[order.Id]);
            }

            line.WorkOrders.AddRange(workOrders);
        }

        return schedules;
    }

    public async Task AddStickingOrderToStickingSchedule(StickingSchedule schedule, StickingOrder order)
    {
        await using var connection = GetConnection();
        await connection.OpenAsync();
        await using var tx = await connection.BeginTransactionAsync();

        var scheduleId = schedule.Id;

        if (scheduleId <= 0) {
            scheduleId = await connection.QuerySingleAsync<int>(@"
INSERT INTO sticking_schedules (date, line_id) VALUES (@date, @lineId) 
ON CONFLICT (date, line_id) 
DO UPDATE SET date = EXCLUDED.date, line_id = EXCLUDED.line_id
RETURNING id;", new { scheduleId, lineId = schedule.LineId, date = DateTime.Parse(schedule.Date) }, transaction: tx);
        }

        var sortOrder = await connection.QuerySingleAsync<int>("SELECT coalesce(max(sort_order), 0) + 1 FROM sticking_work_orders WHERE schedule_id = @scheduleId", new { scheduleId }, transaction: tx);
        var workOrderId = await connection.ExecuteScalarAsync<int>(@"
INSERT INTO sticking_work_orders (schedule_id, sort_order, order_id, order_number, plant_size, plant_crop, customer, cuttings, pots, cases, zone, estimated_hours, crew_size, order_comments) 
VALUES (@scheduleId, @sortOrder, @orderId, @orderNumber, @size, @crop, @customer, @cuttings, @pots, @cases, @zone, @stickingHours, @defaultStickingCrewSize, @notes)
RETURNING sticking_work_orders.id;",
            new { scheduleId, sortOrder, OrderId = order.Id, order.OrderNumber, order.Plant.Size, order.Plant.Crop, Customer = order.Customer.Name, order.Cuttings, order.Pots, order.Cases, Zone = order.StickZone.Name, order.StickingHours, order.Plant.DefaultStickingCrewSize, order.Notes },
            transaction: tx);
        await connection.ExecuteAsync(@"
UPDATE sticking_work_orders SET sort_order = u.sort_order
FROM (SELECT id, row_number() over (order by sort_order, id) sort_order from sticking_work_orders u where schedule_id = @scheduleId) u
where u.id = sticking_work_orders.id;", new { scheduleId }, transaction: tx);

        await connection.ExecuteAsync(@"
INSERT INTO sticking_work_order_varieties (work_order_id, name, cuttings, pots, cases, comment) 
VALUES (@workOrderId, @name, @cuttings, @pots, @cases, @comment);",
            order.Varieties.Select(v => new { workOrderId, v.Name, v.Cuttings, v.Pots, v.Cases, v.Comment }),
            transaction: tx);

        await tx.CommitAsync();
    }

    public async Task<IEnumerable<StickingWorkOrderItem>> GetStickingWorkOrders(DateTime date)
    {
        await using var connection = GetConnection();

        var parameters = new DynamicParameters();
        parameters.Add("schedule_date_param", date, DbType.Date);
        var workOrders = (await connection.QueryAsync<StickingWorkOrderItem>("SELECT * FROM boekestyn_sticking_work_orders(@schedule_date_param);", parameters)).ToList();
        var ids = workOrders.Count == 0 ? "-1" : string.Join(", ", workOrders.Select(w => w.Id));
        var varieties = (await connection.QueryAsync<StickingWorkOrderVariety>($"SELECT * FROM sticking_work_order_varieties WHERE work_order_id IN ({ids})")).ToLookup(v => v.WorkOrderId);
        var labour = (await connection.QueryAsync<StickingWorkOrderLabour>(@$"
SELECT id, work_order_id, crew_size, to_char(cast(start_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') start_time, 
to_char(cast(end_time at time zone 'UTC' at time zone 'EDT' as timestamp), 'yyyy-MM-dd HH24:MI:SS') end_time, comments, final_labour 
FROM sticking_work_order_labour WHERE work_order_id IN ({ids})")).ToLookup(l => l.WorkOrderId);

        foreach (var order in workOrders) {
            if (varieties.Contains(order.Id)) {
                order.Varieties.AddRange(varieties[order.Id]);
            }

            if (labour.Contains(order.Id)) {
                order.Labour.AddRange(labour[order.Id]);
            }
        }

        return workOrders;
    }

    public async Task SortStickingWorkOrders(IEnumerable<StickingWorkOrderSort> workOrders) =>
        await GetConnection().ExecuteAsync("UPDATE sticking_work_orders SET sort_order = @sortOrder WHERE id = @workOrderId;", workOrders);

    public async Task UpdateStickingWorkOrderComment(int id, string? comment) =>
        await GetConnection().ExecuteAsync("UPDATE sticking_work_orders SET sticking_comments = @comment WHERE id = @id;", new { id, comment });

    public async Task UpdateStickingWorkOrderCrewSize(int id, int crewSize) =>
        await GetConnection().ExecuteAsync("UPDATE sticking_work_orders SET crew_size = @crewSize WHERE id = @id;", new { id, crewSize });

    public async Task DeleteStickingWorkOrder(int id)
    {
        await using var connection = GetConnection();
        var scheduleId = await connection.QuerySingleAsync<int>("SELECT schedule_id from sticking_work_orders WHERE id = @id", new { id });
        await connection.ExecuteAsync(@"
DELETE FROM sticking_work_orders WHERE id = @id;
update sticking_work_orders set sort_order = u.sort_order
from (select id, row_number() over (order by sort_order, id) sort_order from sticking_work_orders u where schedule_id = @scheduleId) u
where u.id = sticking_work_orders.id;", new { id, scheduleId });
    }

    public async Task StartStickingLabour(int workOrderId, int crewSize)
    {
        await StopStickingLabour(workOrderId, crewSize);
        await GetConnection().ExecuteAsync("INSERT INTO sticking_work_order_labour (work_order_id, crew_size) values (@workOrderId, @crewSize);", new { workOrderId, crewSize });
    }

    public async Task StopStickingLabour(int workOrderId, int crewSize, string? comments = null, bool finalLabour = false) =>
        await GetConnection().ExecuteAsync("UPDATE sticking_work_order_labour SET end_time = current_timestamp, comments = coalesce(@comments, comments), final_labour = @finalLabour WHERE work_order_id = @workOrderId AND end_time IS NULL;",
            new { workOrderId, crewSize, comments, finalLabour });

    public async Task<IEnumerable<StickingWorkOrderHistory>> GetStickingWorkOrderHistory(string[] orderIds) =>
        await GetConnection().QueryAsync<StickingWorkOrderHistory>("SELECT * FROM sticking_work_order_history(@work_orders_param);", new { work_orders_param = orderIds });

    public async Task<IEnumerable<StickingLabourReportItem>> GetStickingLabourReportItems(DateTime start, DateTime end) =>
        await GetConnection().QueryAsync<StickingLabourReportItem>(@"
select
  s.date schedule_date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer,
  sum(wo.cuttings) cuttings,
  sum(wo.pots) pots,
  avg(wo.crew_size) crew_size,
  sum(wo.estimated_hours) estimated_hours,
  coalesce(sum(ROUND(EXTRACT(EPOCH FROM (l.end_time - l.start_time)) / 3600.0, 2)), 0) as actual_hours,
  coalesce(string_agg(nullif(trim(l.comments), ''), '\n'), '') comments
from
  sticking_schedules s
  join sticking_work_orders wo on s.id = wo.schedule_id
  left join (select * from sticking_work_order_labour where end_time is not null) l on wo.id = l.work_order_id
  left join (select * from sticking_work_order_labour where final_labour = true) f on wo.id = f.work_order_id
where
  s.date between @start and @end
group by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer
order by
  s.date,
  wo.plant_size,
  wo.plant_crop,
  wo.customer;
", new { start, end });

    public class StickingWorkOrderSort
    {
        public int WorkOrderId { get; init; }
        public int SortOrder { get; init; }
    }

    public class StickingWorkOrderHistory
    {
        public string OrderId { get; init; } = string.Empty;
    }
}