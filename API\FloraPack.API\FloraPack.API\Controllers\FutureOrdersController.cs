﻿using FloraPack.API.Reports.CustomerConfirmations;
using FloraPack.API.Reports.FutureOrders;
using FloraPack.API.Reports.Prebooks;
using FloraPack.API.Models;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Holidays;
using FloraPack.API.Repositories.LargeOrders;
using FloraPack.API.Repositories.Phytos;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
using FloraPack.API.Utilities;
// using GrapeCity.ActiveReports.Export.Pdf.Section;
using Microsoft.AspNetCore.Mvc;
// using Reports;

namespace FloraPack.API.Controllers;

[Route("API/Future-Orders")]
public class FutureOrdersController(IConfiguration configuration, FutureOrderRepository futureOrderRepository,
        PrebookRepository prebookRepository, PrebookEmailRepository prebookEmailRepository, PhytoEmailRepository phytoEmailRepository,
        LargeOrderEmailRepository largeOrderEmailRepository, SpireRepository spireRepository, SettingsRepository settingsRepository,
        PrebookReportFactory reportFactory, CustomerConfirmationFactory customerConfirmationFactory, HolidayRepository holidayRepository)
    : FloraPackControllerBase(configuration)
{
    private readonly IConfiguration _configuration = configuration;

    [HttpGet("")]
    public async Task<IActionResult> Index([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var futureOrders = await futureOrderRepository.List(startDate, endDate);

        var response = new IndexResponse(futureOrders);

        return Ok(response);
    }

    [HttpPost("Download")]
    public IActionResult Download([FromBody] FutureOrderListReportModel model)
    {
        var report = FutureOrderListReport.Create(model);

        Response.Headers.Append("Content-Disposition", $"attachment;filename=FutureOrders.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpPost("Items/Download")]
    public IActionResult DownloadItems([FromBody] FutureOrderItemsListReportModel model)
    {
        var report = FutureOrderItemsListReport.Create(model);

        Response.Headers.Append("Content-Disposition", $"attachment;filename=FutureOrderItems.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Items")]
    public async Task<IActionResult> ItemSummary([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var items = await futureOrderRepository.ItemSummary(startDate, endDate);

        var response = new ItemSummaryResponse(items);

        return Ok(response);
    }

    [HttpGet("Upgrade-Items")]
    public async Task<IActionResult> UpgradeItems([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {
        var items = await futureOrderRepository.UpgradeItemList(startDate, endDate);
        var upgradeOptions = await settingsRepository.UpgradeOptions();
        var attachments = await prebookRepository.AttachmentList(startDate, endDate);

        var response = new UpgradeItemsResponse(items, upgradeOptions, attachments);

        return Ok(response);
    }

    [HttpPost("Upgrade-Items/Download")]
    public IActionResult DownloadUpgradeItems([FromBody] FutureOrderUpgradeItemsListReportModel model)
    {
        var report = FutureOrderUpgradeItemsListReport.Create(model);

        Response.Headers.Append("Content-Disposition", $"attachment;filename=UpgradeItems.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpPost("Upgrade-Items/Report")]
    public IActionResult UpgradeItemsReport([FromBody] FutureOrderUpgradeItemsListReportModel model)
    {
        var groupedItems = model.Items.Aggregate(new List<List<UpgradeItem>>(), (groupedItems, item) => {
            var existing = groupedItems.FirstOrDefault(g => UpgradeItemMatchesGroup(item, g));
            if (existing != null) {
                existing.Add(item);
            } else {
                groupedItems.Add(new List<UpgradeItem> { item });
            }

            return groupedItems;
        });

        var items = groupedItems
            .OrderBy(i => i.First().Date)
            .ThenBy(i => i.First().Priority)
            .ThenBy(i => i.First().Season)
            .Select(i => {
                var item = i.First();
                var boxCode = i.Count == 1
                    ? item.BoxCode ?? string.Empty
                    : string.Join("\n", i.Select(i2 => $"{i2.BoxCode} - {i2.OrderQuantity}"));
                return new UpgradeSheet.UpgradeItem {
                    Id = item.Id,
                    Date = item.Date ?? string.Empty,
                    CustomerId = item.CustomerId.GetValueOrDefault(),
                    SpirePartNumber = item.SpirePartNumber,
                    Description = string.Join("\n",
                        (new List<string?>
                            { item.Description, item.Comments, item.UpgradeComments, item.GrowerItemNotes, item.ItemGrowerItemNotes })
                        .Where(u => !string.IsNullOrWhiteSpace(u))
                    ),
                    OrderQuantity = i.Sum(i2 => i2.OrderQuantity),
                    BoxCode = boxCode,
                    Upc = item.Upc ?? string.Empty,
                    UpcFormatted = string.Join("\n",
                        (new List<string?> {
                            string.Join(" | ",
                                (new List<string?> { item.Upc, item.DateCode, item.Retail, item.WeightsAndMeasures ? "W&M" : "" })
                                .Where(u => !string.IsNullOrWhiteSpace(u))),
                            item.UpcComment
                        }).Where(c => !string.IsNullOrWhiteSpace(c))
                    ),
                    UpcApprovalRequired = item.UpcApprovalRequired,
                    Retail = item.Retail ?? string.Empty,
                    DateCode = item.DateCode ?? string.Empty,
                    ContainerPickDescription = item.ContainerPickDescription ?? string.Empty,
                    ProductComingFrom = item.ProductComingFrom ?? string.Empty,
                    Origins = string.IsNullOrWhiteSpace(item.Origins) ? "NA" : item.Origins,
                    Costs = string.IsNullOrWhiteSpace(item.Costs) ? "NA" : item.Costs,
                    PackQuantity = item.PackQuantity,
                    LabourHours = item.LabourHours,
                    RoundedLabourHours = item.RoundedLabourHours,
                    UpgradeConfirmed = item.UpgradeConfirmed,
                    UpgradeConfirmedBy = item.UpgradeConfirmedBy ?? string.Empty,
                    TariffCode = item.TariffCode ?? string.Empty
                };
            })
            .ToList();

        // TODO: Replace with alternative PDF generation
        // var report = new UpgradeSheet(items);
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;

        // Response.Headers.Append("Content-Disposition", "inline");
        // return File(memStream, "application/pdf");

        return BadRequest("Report generation temporarily disabled - GrapeCity ActiveReports not available");
    }

    [HttpGet("{id:int}")]
    public async Task<IActionResult> Detail(int id)
    {
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return NotFound();
        }

        var prebooks = await prebookRepository.DetailsForFutureOrder(id);

        var emails = await prebookEmailRepository.GetEmailsForFutureOrder(id);

        var response = new FutureOrderDetailResponse(futureOrder, prebooks, emails);
        return Ok(response);
    }

    [HttpPost("")]
    public async Task<IActionResult> Create([FromBody] FutureOrderCreate futureOrder)
    {
        var who = await UserEmail();
        var customerItemCodeByShipTo = false;
        if (futureOrder.CustomerId.HasValue) {
            var customerSetting = await settingsRepository.GetCustomerSetting(futureOrder.CustomerId.Value);
            customerItemCodeByShipTo = customerSetting.CustomerItemCodeByShipTo;
        }

        if (futureOrder.RequiredDate.HasValue) {
            foreach (var item in futureOrder.Items.Where(i => i.VendorId == Constants.BoekestynVendorId)) {
                if (!string.IsNullOrWhiteSpace(item.BoekestynPlantId) && !string.IsNullOrWhiteSpace(item.BoekestynCustomerAbbreviation)) {

                    var productDefault = await settingsRepository.GetProductDefault(item.SpireInventoryId);

                    var labourHours = productDefault?.UpgradeLabourHours;

                    if (item.UpgradeLabourHours > 0) {
                        labourHours = item.UpgradeLabourHours;
                    }

                    var quantityPerFinishedItem = default(int?);

                    if (item.QuantityPerFinishedItem.GetValueOrDefault() > 0) {
                        quantityPerFinishedItem = item.QuantityPerFinishedItem.GetValueOrDefault();
                    }

                    await settingsRepository.UpdateProductDefault(item.SpireInventoryId, item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, labourHours, quantityPerFinishedItem, (productDefault?.IsUpgrade).GetValueOrDefault(), (productDefault?.IgnoreOverrideQuantity).GetValueOrDefault());
                }
            }
        }

        var id = await futureOrderRepository.Create(futureOrder, who, customerItemCodeByShipTo);

        if (futureOrder.Items.Any(i => i.PhytoRequired)) {
            var detail = (await futureOrderRepository.Detail(id))!;
            var to = new List<string>();
#if DEBUG
            to.Add("<EMAIL>");
#else
            to.Add("<EMAIL>");
            to.Add("<EMAIL>");
#endif
            var boxCode = string.IsNullOrWhiteSpace(futureOrder.BoxCode) ? string.Empty : $"| {futureOrder.BoxCode} ";
            var requiredDate = detail.RequiredDate.HasValue ? detail.RequiredDate.Value.ToString("yyyy-MM-dd") : "Not Specified";
            var clientUrlBase = _configuration["ClientUrl"]!;
            var link = $"{clientUrlBase}/future-orders/{id}";
            var subject = $"[PHYTO NOTIFICATION] Future Order #{id:00000} {boxCode}| {requiredDate}";
            var body = $@"<p>Future Order <a href='{link}'>#{id:00000}</a> has been created and requires a Phyto.</p>";

            await SendMail(subject, body, to);

            await phytoEmailRepository.Create(detail, to, subject, body, who!);
        }

        var prebooks = await prebookRepository.DetailsForFutureOrder(id);
        foreach (var prebook in prebooks.Where(p =>
                     (p.VendorId == Constants.BoekestynVendorId || p.VendorId == Constants.UpgradesVendorId) &&
                     p.Items.Any(i => i.OrderQuantity >= LargeOrderEmailRepository.LargeOrderThreshold && PackQuantityParser.Parse(i.Description) > 1))) {
            if (await largeOrderEmailRepository.HasChanged(prebook)) {
                var to = new List<string>();
                if (prebook.RequiredDate.HasValue && prebook.RequiredDate.Value > DateTime.Today.AddMonths(4)) {
#if DEBUG
                    to.Add("<EMAIL>");
#else
                    to.Add("<EMAIL>");
                    to.Add("<EMAIL>");
#endif
                } else {
#if DEBUG
                    to.Add("<EMAIL>");
#else
                    to.Add("<EMAIL>");
#endif
                }

                var boxCode = string.IsNullOrWhiteSpace(futureOrder.BoxCode)
                    ? string.Empty
                    : $"| {futureOrder.BoxCode} ";
                var requiredDate = futureOrder.RequiredDate.HasValue
                    ? futureOrder.RequiredDate.Value.ToString("yyyy-MM-dd")
                    : "Not Specified";
                var clientUrlBase = _configuration["ClientUrl"]!;
                var link = $"{clientUrlBase}/prebooks/{prebook.Id}";
                var subject = $"[LARGE ORDER NOTIFICATION] Prebook #{prebook.Id:00000} {boxCode}| {requiredDate}";
                var body =
                    $@"<p>Prebook <a href='{link}'>#{prebook.Id:00000}</a> has been updated and has one or more items of 100 cases or more.</p>";

                var report = await reportFactory.CreateReport(prebook.Id);
                if (report == null) {
                    return NotFound();
                }

                // TODO: Replace with alternative PDF generation
                // report.Run();
                // var export = new PdfExport();
                // var memStream = new MemoryStream();
                // export.Export(report.Document, memStream);
                // memStream.Position = 0;
                // var reports = new List<EmailAttachment> {
                //     new (memStream.ToArray(), $"Prebook {prebook.Id:0000}.pdf", "application/pdf")
                // };

                // Temporarily disable email with PDF attachment
                return BadRequest("Email with PDF attachment temporarily disabled - GrapeCity ActiveReports not available");

                // await SendMail(subject, body, to, reports: reports);
                // await largeOrderEmailRepository.Create(prebook, to, subject, body, who!);
            }
        }

        var url = Url.Action(nameof(Detail), new { id });
        return url == null ? throw new ArgumentOutOfRangeException(nameof(futureOrder)) : Created(url, new { id });
    }

    [HttpPut("{id:int}")]
    public async Task<IActionResult> Update([FromBody] UpdateFutureOrderModel model)
    {
        var who = await UserEmail();

        var existing = await prebookRepository.DetailsForFutureOrder(model.FutureOrder.Id);

        var customerItemCodeByShipTo = false;
        if (model.FutureOrder.CustomerId.HasValue) {
            var customerSetting = await settingsRepository.GetCustomerSetting(model.FutureOrder.CustomerId.Value);
            customerItemCodeByShipTo = customerSetting.CustomerItemCodeByShipTo;
        }

        // add new ones
        if (model.FutureOrder.RequiredDate.HasValue) {

            foreach (var item in model.FutureOrder.Items.Where(i => i.VendorId == Constants.BoekestynVendorId)) {
                if (!string.IsNullOrWhiteSpace(item.BoekestynPlantId) && !string.IsNullOrWhiteSpace(item.BoekestynCustomerAbbreviation)) {

                    var productDefault = await settingsRepository.GetProductDefault(item.SpireInventoryId);

                    var labourHours = productDefault?.UpgradeLabourHours;
                    var quantityPerFinishedItem = productDefault?.QuantityPerFinishedItem;

                    if (item.UpgradeLabourHours > 0) {
                        labourHours = item.UpgradeLabourHours;
                    }

                    if (item.QuantityPerFinishedItem.GetValueOrDefault() > 0) {
                        quantityPerFinishedItem = item.QuantityPerFinishedItem.GetValueOrDefault();
                    }

                    await settingsRepository.UpdateProductDefault(item.SpireInventoryId, item.BoekestynPlantId, item.BoekestynCustomerAbbreviation, labourHours, quantityPerFinishedItem,
                        (productDefault?.IsUpgrade).GetValueOrDefault(), (productDefault?.IgnoreOverrideQuantity).GetValueOrDefault());
                }
            }
        }

        var futureOrderItemMap = await futureOrderRepository.Update(model.FutureOrder, who, customerItemCodeByShipTo);
        foreach (var prebook in model.Prebooks.Where(p => p.Id > 0)) {
            await prebookRepository.Update(prebook, who, futureOrderItemMap);
        }

        var existingPrebookIds = existing.Select(p => p.Id).ToList();
        var submittedPrebookIds = model.Prebooks.Where(p => p.Id > 0).Select(p => p.Id).ToList();
        var deleted = existingPrebookIds.Except(submittedPrebookIds);
        foreach (var deleteId in deleted) {
            await prebookRepository.Delete(deleteId, who!, true);
        }

        var id = model.FutureOrder.Id;
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return NotFound();
        }

        if (await phytoEmailRepository.HasChanged(futureOrder)) {
            var to = new List<string>();
#if DEBUG
            to.Add("<EMAIL>");
#else
            to.Add("<EMAIL>");
            to.Add("<EMAIL>");
#endif
            var boxCode = string.IsNullOrWhiteSpace(futureOrder.BoxCode) ? string.Empty : $"| {futureOrder.BoxCode} ";
            var requiredDate = futureOrder.RequiredDate.HasValue ? futureOrder.RequiredDate.Value.ToString("yyyy-MM-dd") : "Not Specified";
            var clientUrlBase = _configuration["ClientUrl"]!;
            var link = $"{clientUrlBase}/future-orders/{id}";
            var subject = $"[PHYTO NOTIFICATION] Future Order #{id:00000} {boxCode}| {requiredDate}";
            var body = $@"<p>Future Order <a href='{link}'>#{id:00000}</a> has been updated and requires a Phyto.</p>";

            await futureOrderRepository.ClearPhytoOrdered(id);

            await SendMail(subject, body, to);

            await phytoEmailRepository.Create(futureOrder, to, subject, body, who);
        }

        var prebooks = await prebookRepository.DetailsForFutureOrder(id);

        foreach (var prebook in prebooks.Where(p =>
                     (p.VendorId == Constants.BoekestynVendorId || p.VendorId == Constants.UpgradesVendorId) &&
                     p.Items.Any(i => i.OrderQuantity >= LargeOrderEmailRepository.LargeOrderThreshold && PackQuantityParser.Parse(i.Description) > 1))) {
            if (await largeOrderEmailRepository.HasChanged(prebook)) {
                var to = new List<string>();
                if (prebook.RequiredDate.HasValue && prebook.RequiredDate.Value > DateTime.Today.AddMonths(4)) {
#if DEBUG
                    to.Add("<EMAIL>");
#else
                    to.Add("<EMAIL>");
                    to.Add("<EMAIL>");
#endif
                } else {
#if DEBUG
                    to.Add("<EMAIL>");
#else
                    to.Add("<EMAIL>");
#endif
                }

                var boxCode = string.IsNullOrWhiteSpace(futureOrder.BoxCode)
                    ? string.Empty
                    : $"| {futureOrder.BoxCode} ";
                var requiredDate = futureOrder.RequiredDate.HasValue
                    ? futureOrder.RequiredDate.Value.ToString("yyyy-MM-dd")
                    : "Not Specified";
                var clientUrlBase = _configuration["ClientUrl"]!;
                var link = $"{clientUrlBase}/prebooks/{prebook.Id}";
                var subject = $"[LARGE ORDER NOTIFICATION] Prebook #{prebook.Id:00000} {boxCode}| {requiredDate}";
                var body =
                    $@"<p>Prebook <a href='{link}'>#{prebook.Id:00000}</a> has been updated and has one or more items of 100 cases or more.</p>";

                var report = await reportFactory.CreateReport(prebook.Id);
                if (report == null) {
                    return NotFound();
                }

                // TODO: Replace with alternative PDF generation
                // report.Run();
                // var export = new PdfExport();
                // var memStream = new MemoryStream();
                // export.Export(report.Document, memStream);
                // memStream.Position = 0;
                // var reports = new List<EmailAttachment> {
                //     new (memStream.ToArray(), $"Prebook {prebook.Id:0000}.pdf", "application/pdf")
                // };

                // Temporarily disable email with PDF attachment
                return BadRequest("Email with PDF attachment temporarily disabled - GrapeCity ActiveReports not available");

                // await SendMail(subject, body, to, reports: reports);
                // await largeOrderEmailRepository.Create(prebook, to, subject, body, who!);
            }
        }

        var emails = await prebookEmailRepository.GetEmailsForFutureOrder(id);
        
        var response = new FutureOrderDetailResponse(futureOrder, prebooks, emails);
        return Ok(response);
    }

    [HttpPost("{id:int}/Split")]
    public async Task<IActionResult> Split(int id, [FromBody] FutureOrderSplit model)
    {
        var who = await UserEmail();

        var createdId = await futureOrderRepository.Split(id, model, who);

        var url = Url.Action(nameof(Detail), new { Id = createdId });
        return url == null ? throw new ArgumentOutOfRangeException(nameof(model)) : Created(url, new { Id = createdId });
    }

    [HttpGet("{id:int}/Children")]
    public async Task<IActionResult> Children(int id)
    {
        var children = await futureOrderRepository.GetChildren(id);

        var response = new ChildrenResponse(children);

        return Ok(response);
    }

    [HttpPost("{id:int}/Spire")]
    public async Task<IActionResult> CreateSalesOrder(int id)
    {
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return NotFound();
        }

        if (futureOrder.SpireSalesOrderId.HasValue) {
            await spireRepository.DeleteSalesOrder(futureOrder.SpireSalesOrderId.Value);
        }

        var prebooks = await prebookRepository.DetailsForFutureOrder(id);

        var po = await spireRepository.CreateSalesOrder(futureOrder, prebooks) ?? throw new ApplicationException("Spire Sales Order not created.");
        var who = await UserEmail() ?? "Unknown";
        await futureOrderRepository.SetSpireSalesOrder(id, po.Id, po.OrderNumber, who);

        var response = new CreateSalesOrderResponse(po.Id, po.OrderNumber);
        return Ok(response);
    }
    
    [HttpGet("Vendor-Defaults")]
    public async Task<IActionResult> VendorDefaults([FromQuery] int? vendorId = null, [FromQuery] string? vendorNumber = null)
    {
        if (vendorId == null && !string.IsNullOrWhiteSpace(vendorNumber)) {
            var vendor = await spireRepository.GetVendorByNumber(vendorNumber);
            if (vendor != null) {
                vendorId = vendor.Id;
            }
        }

        var defaults = await futureOrderRepository.GetVendorDefault(vendorId.GetValueOrDefault());

        var response = new VendorDefaultsResponse(defaults);
        return Ok(response);
    }

    [HttpGet("Deleted")]
    public async Task<IActionResult> Deleted()
    {
        var deleted = await futureOrderRepository.DeletedOrders();

        var response = new DeletedResponse(deleted);

        return Ok(response);
    }

    [HttpPut("{id:int}/Delete")]
    public async Task<IActionResult> Delete(int id)
    {
        var who = await UserEmail() ?? "Unknown";

        await futureOrderRepository.Delete(id, who);
        
        return Ok();
    }

    [HttpPut("{id:int}/Undelete")]
    public async Task<IActionResult> Undelete(int id)
    {
        await futureOrderRepository.Undelete(id);
        
        return Ok();
    }

    [HttpPatch("Upgrade-Items/{id:int}")]
    public async Task<IActionResult> UpdateUpgradeItemProperty(int id, [FromBody] UpdateUpgradeItemPropertyModel model)
    {
        if (model.IsNumeric) {
            await prebookRepository.UpdateUpgradeItemProperty(id, model.FieldName, model.ValueAsNumber);
        } else if (model.IsBoolean) {
            await prebookRepository.UpdateUpgradeItemProperty(id, model.FieldName, model.ValueAsBoolean);
        } else {
            await prebookRepository.UpdateUpgradeItemProperty(id, model.FieldName, model.Value);
        }


        return Ok();
    }

    [HttpGet("Items/{id:int}/Price")]
    public async Task<IActionResult> ItemPrice(int id, [FromQuery] int? customerId = null, [FromQuery] int? shipToId = null, [FromQuery] DateTime? requiredDate = null)
    {
        var price = await spireRepository.GetItemPrice(id, customerId, shipToId, requiredDate);
        var response = new ItemPriceResponse(price);
        return Ok(response);
    }

    [HttpGet("Items/Prices")]
    public async Task<IActionResult> ItemPrice([FromQuery] int? customerId = null, [FromQuery] int? shipToId = null, [FromQuery] DateTime? requiredDate = null)
    {
        var ids = Request.Query.Keys.Where(k => int.TryParse(k, out _)).Select(int.Parse).ToList();

        var price = await spireRepository.GetItemPrices(customerId, shipToId, requiredDate, ids);
        var response = new ItemsPriceResponse(price);
        return Ok(response);
    }

    [HttpGet("Holidays")]
    public async Task<IActionResult> GetHolidays([FromQuery] int year)
    {
        var holidays = await holidayRepository.GetHolidays(year);
        return Ok(holidays);
    }

    [HttpPost("{id:int}/Customer-Confirmation/Email")]
    public async Task<IActionResult> CreateCustomerConfirmationEmail(int id, [FromBody] CustomerConfirmationEmailModel model)
    {
        var report = await customerConfirmationFactory.CreateReport(id, model.IncludeZeroQuantity);
        if (report == null) {
            return NotFound();
        }

        // TODO: Replace with alternative PDF generation
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;
        // var reports = new List<EmailAttachment> {
        //     new (memStream.ToArray(), $"Customer Confirmation {id:0000}.pdf", "application/pdf")
        // };

        // Temporarily disable email with PDF attachment
        return BadRequest("Email with PDF attachment temporarily disabled - GrapeCity ActiveReports not available");

        // var htmlBody = model.Body.Replace("\n", "<br />");
        // var to = model.To.Split(";").Select(to => to.Trim());
        // var cc = model.Cc?.Split(";").Select(t => t.Trim()).ToList() ?? [];

        // var bcc = model.Bcc?.Split(";").Select(t => t.Trim());
        // await SendMail(model.Subject, htmlBody, to, cc, bcc, reports);

        // return Ok();
    }

    private record IndexResponse(IEnumerable<FutureOrderListItem> FutureOrders);

    public class FutureOrderListReportModel
    {
        public List<FutureOrderListItem> Items { get; set; } = [];
    }

    public class FutureOrderItemsListReportModel
    {
        public List<FutureOrderSummaryItem> Items { get; set; } = [];
    }

    public class FutureOrderUpgradeItemsListReportModel
    {
        public List<UpgradeItem> Items { get; set; } = [];
    }

    private record ItemSummaryResponse(IEnumerable<FutureOrderSummaryItem> Items);
    private record UpgradeItemsResponse(IEnumerable<UpgradeItem> Items, IEnumerable<UpgradeOption> UpgradeOptions, IEnumerable<PrebookItemAttachment> Attachments);
    private record FutureOrderDetailResponse(FutureOrderDetail FutureOrder, IEnumerable<PrebookDetail> Prebooks, IEnumerable<PrebookEmail> Emails);
    private record ProductCustomerDefaultsResponse(IEnumerable<ProductCustomerDefault> ProductCustomerDefaults);
    private record VendorDefaultsResponse(VendorDefault Default);

    public class UpdateFutureOrderModel
    {
        public FutureOrderUpdate FutureOrder { get; set; } = new();
        public List<PrebookDetail> Prebooks { get; set; } = [];
    }

    private record CreateSalesOrderResponse(int SpireSalesOrderId, string SpireSalesOrderNumber);

    private record DeletedResponse(IEnumerable<DeletedFutureOrder> Orders);

    public class UpdateUpgradeItemPropertyModel
    {
        public string FieldName { get; set; } = string.Empty;
        public string? Value { get; set; }
        public decimal ValueAsNumber { get; set; }
        public bool ValueAsBoolean { get; set; }
        public bool IsNumeric { get; set; }
        public bool IsBoolean { get; set; }
    }

    private record ItemPriceResponse(decimal? Price);

    private record ItemsPriceResponse(List<SpireRepository.SpireItemPrice> Prices);

    private record ChildrenResponse(IEnumerable<FutureOrderChild> Children);

    public class CustomerConfirmationEmailModel
    {
        public string To { get; set; } = string.Empty;
        public string? Cc { get; set; }
        public string? Bcc { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IncludeZeroQuantity { get; set; }
    }

    private bool UpgradeItemMatchesGroup(UpgradeItem item, List<UpgradeItem> group) =>
        item.UpgradeConfirmed != null &&
        group.Any(g => g.UpgradeConfirmed != null &&
                       g.CustomerId == item.CustomerId &&
                       string.Equals(g.Description, item.Description, StringComparison.InvariantCultureIgnoreCase) &&
                       string.Equals(g.ContainerPickDescription, item.ContainerPickDescription,
                           StringComparison.InvariantCultureIgnoreCase) &&
                       string.Equals(g.Upc, item.Upc, StringComparison.InvariantCultureIgnoreCase) &&
                       string.Equals(g.DateCode, item.DateCode, StringComparison.InvariantCultureIgnoreCase) &&
                       string.Equals(g.Retail, item.Retail, StringComparison.InvariantCultureIgnoreCase) &&
                       string.Equals(g.ProductComingFrom, item.ProductComingFrom,
                           StringComparison.InvariantCultureIgnoreCase)

        );
}