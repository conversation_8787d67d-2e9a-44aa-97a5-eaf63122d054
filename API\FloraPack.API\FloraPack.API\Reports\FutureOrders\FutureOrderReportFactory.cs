﻿using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
using FloraPack.API.Utilities;

namespace FloraPack.API.Reports.FutureOrders;

public class FutureOrderReportFactory(FutureOrderRepository futureOrderRepository, PrebookRepository prebookRepository,
    PrebookEmailRepository prebookEmailRepository, SpireRepository spireRepository, SettingsRepository settingsRepository)
{
    private static string? GetPriceWarning(int? customerId, int? shipToId, FutureOrderDetailItem item, decimal? availabilityPrice, List<PriceDeviationWarning> priceDeviationWarnings)
    {
        var casePrice = item.UnitPrice.GetValueOrDefault();
        var packQuantity = PackQuantityParser.Parse(item.Description);
        var unitPrice = (packQuantity.HasValue && packQuantity.Value != 0)
            ? availabilityPrice / packQuantity.Value
            : 0;

        if(item.UseAvailabilityPricing || item.SpecialPrice.HasValue || availabilityPrice == null || casePrice == 0 || unitPrice == 0) {
            return null;
        }

        var devianceScores = priceDeviationWarnings
            .Where((w) => {
                if (w.CustomerId != null && customerId != w.CustomerId) {
                    return false;
                }

                if (w.ShipToId != null && shipToId != w.ShipToId) {
                    return false;
                }

                if (w.MaxUnitPrice != null && unitPrice > w.MaxUnitPrice) {
                    return false;
                }

                if (w.MinPackSize != null && packQuantity > w.MinPackSize) {
                    return false;
                }

                return true;
            })
            .Select((w) => {
                var score =
                    (w.CustomerId != null && customerId == w.CustomerId ? 1 : 0) +
                    (w.ShipToId != null && shipToId == w.ShipToId ? 1 : 0) +
                    (w.MaxUnitPrice != null && unitPrice < w.MaxUnitPrice ? 1 : 0) +
                    (w.MinPackSize != null && packQuantity < w.MinPackSize ? 1 : 0);

                return new { score, w };
            })
            .OrderBy(w => w.score);
        var deviance = devianceScores.FirstOrDefault()?.w;
        var difference = (1 - casePrice / availabilityPrice) * 100;

        if (deviance == null) {
            return null;
        }

        if (difference <= deviance.AllowableDeviation) {
            return null;
        }

        return $"Avail: {availabilityPrice:C2}";
    }

    public async Task<FutureOrderSummary?> CreateReport(int id)
    {
        var futureOrder = await futureOrderRepository.Detail(id);
        if (futureOrder == null) {
            return null;
        }

        var prebooks = await prebookRepository.DetailsForFutureOrder(id);

        var itemIds = futureOrder.Items.Select(i => i.SpireInventoryId).ToList();
        var availabilityPrices =
            await spireRepository.GetItemPrices(futureOrder.CustomerId, futureOrder.ShipToId, futureOrder.RequiredDate, itemIds);

        var priceDeviationWarnings = await settingsRepository.PriceDeviationWarnings();
        
        var emails = await prebookEmailRepository.GetEmailsForFutureOrder(id);
        var sentItems = emails.Aggregate(new List<int>(), (list, email) => {
            list.AddRange(email.Items
                .Where(i => i.PrebookItemId.HasValue)
                .Select(i => i.PrebookItemId.GetValueOrDefault()));
            return list;
        });

        var availabilityPricingIds = futureOrder.Items
            .Where(i => i.UseAvailabilityPricing)
            .Select(i => i.SpireInventoryId)
            .Distinct()
            .ToList();
        var prices = await spireRepository.GetItemPrices(futureOrder.CustomerId, futureOrder.ShipToId, futureOrder.RequiredDate, availabilityPricingIds);
        foreach (var price in prices) {
            futureOrder.Items
                .Where(i => i.UseAvailabilityPricing && i.SpireInventoryId == price.SpireInventoryItemId)
                .ToList()
                .ForEach(item => item.UnitPrice = price.Price);
        }

        var shipTo = futureOrder.ShipToId.HasValue ? await spireRepository.GetAddressDetail(futureOrder.ShipToId.Value) : null;

        SalesOrderDetail? spireOrder = null;
        if (futureOrder.SpireSalesOrderId.HasValue) {
            try {
                spireOrder = await spireRepository.SalesOrderDetail(futureOrder.SpireSalesOrderId.Value);
            } catch { /* probably invoiced */ }
        }

        var items = futureOrder.Items
            .OrderBy(i => i.SortOrder)
            .Select(i => {
                var spireItem = spireOrder?.Items.Find(s => s.Id == i.SpireSalesOrderItemId);
                var spireCreateItem = new SalesOrderCreate.SalesOrderItem(i.Id, 0, futureOrder.CustomerId ?? 0,
                    i.SpireInventoryId,
                    i.OrderQuantity, i.VendorName ?? string.Empty, i.Comments, i.PotCover, i.Upc, i.Retail, i.DateCode,
                    i.WeightsAndMeasures, i.UnitPrice, i.CustomerItemCode, i.SpecialPrice, 0, i.GrowerItemNotes);
                var commentsChange = spireOrder != null && !string.Equals(spireItem?.Comment, spireCreateItem.Comment, StringComparison.InvariantCultureIgnoreCase);
                var availabilityPrice = availabilityPrices.FirstOrDefault(p => p.SpireInventoryItemId == i.SpireInventoryId)?.Price;
                var priceWarning = GetPriceWarning(futureOrder.CustomerId, futureOrder.ShipToId, i, availabilityPrice, priceDeviationWarnings);
                var headerGrowerItemNotes = futureOrder.GrowerItemNotes ?? string.Empty;
                var itemGrowerItemNotes = i.GrowerItemNotes ?? string.Empty;
                var growerItemNotes = headerGrowerItemNotes +
                    (string.IsNullOrEmpty(headerGrowerItemNotes) || string.IsNullOrEmpty(itemGrowerItemNotes) ? string.Empty : "\n") +
                    itemGrowerItemNotes;
                return new FutureOrderSummaryReportFutureOrderItem {
                    PrebookId = prebooks.FirstOrDefault(p => p.Items.Any(pi => pi.FutureOrderItemId == i.Id))?.Id
                        .ToString("00000") ?? string.Empty,
                    VendorId = i.VendorId.GetValueOrDefault(),
                    VendorName = i.VendorName ?? string.Empty,
                    SpirePartNumber = i.SpirePartNumber,
                    Description = i.Description,
                    OrderQuantity = i.OrderQuantity,
                    IsApproximate = i.IsApproximate,
                    PotCover = i.HasPotCover ? (i.PotCover ?? string.Empty) : string.Empty,
                    DateCode = i.DateCode ?? string.Empty,
                    Upc = i.Upc ?? string.Empty,
                    WeightsAndMeasures = i.WeightsAndMeasures,
                    Retail = i.Retail ?? string.Empty,
                    UnitPrice = i.UnitPrice,
                    CustomerItemCode = i.CustomerItemCode ?? string.Empty,
                    Comments = i.Comments ?? string.Empty,
                    GrowerItemNotes = growerItemNotes,
                    SpecialPrice = i.SpecialPrice,
                    PriceWarning = priceWarning,
                    UpgradeSheet = i.UpgradeSheet,
                    // newly-added since it was sent to spire
                    IsNew = futureOrder.SpireSalesOrderId.HasValue && i.SpireSalesOrderItemId == null,
                    SpireOrderQuantity = spireItem?.OrderQty,
                    SpireCommentFieldsChanged = commentsChange,
                    Sent = prebooks.Any(p =>
                        p.Items.Any(pi => pi.FutureOrderItemId == i.Id && sentItems.Contains(pi.Id)))
                };
            })
            .ToList();

        var reportFutureOrder = new FutureOrderSummaryReportFutureOrder {
            Id = futureOrder.Id,
            SpireSalesOrderNumber = futureOrder.SpireSalesOrderNumber ?? string.Empty,
            RequiredDate = futureOrder.RequiredDate,
            ArrivalDate = futureOrder.ArrivalDate.HasValue ? futureOrder.ArrivalDate.Value.ToString("MMM d, yyyy") : (futureOrder.SeasonName ?? string.Empty),
            CustomerName = futureOrder.CustomerName ?? string.Empty,
            ShipToName = futureOrder.ShipToName ?? string.Empty,
            BoxCode = futureOrder.BoxCode ?? string.Empty,
            RequiresLabels = futureOrder.RequiresLabels,
            CustomerPurchaseOrderNumber = futureOrder.CustomerPurchaseOrderNumber ?? string.Empty,
            SalespersonName = futureOrder.SalespersonName ?? string.Empty,
            ShipViaName = futureOrder.ShipViaName ?? string.Empty,
            TotalOrderQuantity = items.Sum(i => i.OrderQuantity),
            TotalPrice = items.Sum(i => i.UnitPrice.GetValueOrDefault() * i.OrderQuantity),
            RequiresPhytos = futureOrder.Items.Any(i => i.PhytoRequired),
            SpireNotes = futureOrder.SpireNotes ?? string.Empty,
            InternalNotes = string.Join("\n\n", futureOrder.Comments.Select(c => $"{c.CreatedBy}: {c.Created:MMM d, yyyy h:mm}\n{c.Comments}")),
            FreightPerLoad = futureOrder.FreightPerLoad,
            FreightPerCase = futureOrder.FreightPerCase,
            FreightIsActual = futureOrder.FreightIsActual,
            SpecialLabelDetails = shipTo?.ShipTo.Udf?.SpecialLabelDetails ?? string.Empty
        };

        var report = new FutureOrderSummary(reportFutureOrder, items);
        return report;
    }
}