using System;
using System.Collections.Generic;
using System.Globalization;

namespace FloraPack.API.Reports
{
    public partial class CobornsLabels : GrapeCity.ActiveReports.SectionReport
    {
        public CobornsLabels(List<CobornsLabelItem> items)
        {
            InitializeComponent();

            DataSource = items;
        }

        private void OnReportStart(object sender, EventArgs e)
        {
            Document.Printer.PrinterName = string.Empty;
        }
    }

    public class CobornsLabelItem
    {
        public string StoreName { get; set; } = string.Empty;
        public int? StoreNumber { get; set; }
        public string ProductNumber { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UPC { get; set; } = string.Empty;
        public string Size { get; set; } = string.Empty;
        public DateTime ReceivingDate { get; set; }
        public string PurchaseOrderNumber { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int LabelNumber { get; set; }
        public int LabelCount { get; set; }

        public string DescriptionDisplay => $"{Description} {Size} x {PackQuantity}";
        public string DeliveryDateDisplay => "Delivery Date - " + ReceivingDate.ToString("M/d/yyyy", CultureInfo.InvariantCulture);
        public string PurchaseOrderNumberDisplay => string.IsNullOrWhiteSpace(PurchaseOrderNumber) ? string.Empty : $"PO # - {PurchaseOrderNumber}";
        public string UPCDisplay => UPC;
        public string LabelNumberDisplay => $"{LabelNumber} of {LabelCount}";
        public string StoreNumberDisplay => $"Store #{StoreNumber}";
    }
}