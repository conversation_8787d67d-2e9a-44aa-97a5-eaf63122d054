using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text.RegularExpressions;
// using GrapeCity.ActiveReports;

namespace Reports
{
    // TODO: Replace with alternative report implementation
    // public partial class UpgradeSheet : GrapeCity.ActiveReports.SectionReport
    /*
    {
        private int _formatIndex;
        private int _printIndex;

        public UpgradeSheet(List<UpgradeItem> items)
        {
            InitializeComponent();

            txtDate.Value = DateTime.Today;
            var requiredDate = items.FirstOrDefault()?.Date ?? string.Empty;
            txtRequiredDate.Value = DateTime.TryParse(requiredDate, out var date) ? date : DateTime.Today;

            DataSource = items;
        }

        public class UpgradeItem
        {
            public int Id { get; set; }
            public string Date { get; set; } = string.Empty;
            public int CustomerId { get; set; }
            public string SpirePartNumber { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public int OrderQuantity { get; set; }
            public string BoxCode { get; set; } = string.Empty;
            public string Upc { get; set; } = string.Empty;
            public string UpcFormatted { get; set; } = string.Empty;
            public bool UpcApprovalRequired { get; set; }
            public string DateCode { get; set; } = string.Empty;
            public string Retail { get; set; } = string.Empty;
            public string ContainerPickDescription { get; set; } = string.Empty;
            public string ProductComingFrom { get; set; } = string.Empty;
            public string Origins { get; set; } = string.Empty;
            public string Costs { get; set; } = string.Empty;
            public int? PackQuantity { get; set; }
            public int RoundedLabourHours { get; set; }
            public decimal LabourHours { get; set; }
            public DateTime? UpgradeConfirmed { get; set; }
            public string UpgradeConfirmedBy { get; set; } = string.Empty;
            public string TariffCode { get; set; } = string.Empty;
        }

        private void OnDetailBeforePrint(object sender, EventArgs e)
        {
            txtQty.Height = detail.Height;
            txtPackQuantity.Height = detail.Height;
            txtDescription.Height = detail.Height;
            txtProductComingFrom.Height = detail.Height;
            txtUPC.Height = detail.Height;
            txtUPCApprovalRequired.Height = detail.Height;
            txtContainerPickDescription.Height = detail.Height;
            txtOrigins.Height = detail.Height;
            txtCosts.Height = detail.Height;
            txtBoxCode.Height = detail.Height;
            txtLabourHours.Height = detail.Height;

            if (DataSource is List<UpgradeItem> items && _printIndex < items.Count) {
                var item = items[_printIndex];

                var shipToItems = items
                    .Where(i => item.UpgradeConfirmed != null
                                                   && i.Date == item.Date
                                                   && i.CustomerId == item.CustomerId
                                                   && string.Equals(i.Description, item.Description, StringComparison.InvariantCultureIgnoreCase)
                                                   && string.Equals(i.ContainerPickDescription, item.ContainerPickDescription, StringComparison.InvariantCultureIgnoreCase)
                                                   && string.Equals(i.Upc, item.Upc, StringComparison.InvariantCultureIgnoreCase)
                                                   && string.Equals(i.DateCode, item.DateCode, StringComparison.InvariantCultureIgnoreCase)
                                                   && string.Equals(i.Retail, item.Retail, StringComparison.InvariantCultureIgnoreCase)
                                                   && string.Equals(i.ProductComingFrom, item.ProductComingFrom, StringComparison.CurrentCultureIgnoreCase)
                            )
                    .ToList();
                var nextIndex = _printIndex + shipToItems.Count;

                var borderStyle = (nextIndex >= items.Count || items[nextIndex].BoxCode != item.BoxCode) ? BorderLineStyle.ExtraThickSolid : BorderLineStyle.Solid;

                txtQty.Border.BottomStyle = borderStyle;
                txtPackQuantity.Border.BottomStyle = borderStyle;
                txtDescription.Border.BottomStyle = borderStyle;
                txtProductComingFrom.Border.BottomStyle = borderStyle;
                txtUPC.Border.BottomStyle = borderStyle;
                txtUPCApprovalRequired.Border.BottomStyle = borderStyle;
                txtContainerPickDescription.Border.BottomStyle = borderStyle;
                txtOrigins.Border.BottomStyle = borderStyle;
                txtCosts.Border.BottomStyle = borderStyle;
                txtBoxCode.Border.BottomStyle = borderStyle;
                txtLabourHours.Border.BottomStyle = borderStyle;

                _printIndex = nextIndex;
            }
        }

        private void OnDetailFormat(object sender, EventArgs e)
        {
            if (DataSource is List<UpgradeItem> items && _formatIndex < items.Count) {
                var item = items[_formatIndex];

                var shipToItems = items
                    .Where(i => item.UpgradeConfirmed != null
                                && i.Date == item.Date
                                && i.CustomerId == item.CustomerId
                                && string.Equals(i.Description, item.Description, StringComparison.InvariantCultureIgnoreCase)
                                && string.Equals(i.ContainerPickDescription, item.ContainerPickDescription, StringComparison.InvariantCultureIgnoreCase)
                                && string.Equals(i.Upc, item.Upc, StringComparison.InvariantCultureIgnoreCase)
                                && string.Equals(i.DateCode, item.DateCode, StringComparison.InvariantCultureIgnoreCase)
                                && string.Equals(i.Retail, item.Retail, StringComparison.InvariantCultureIgnoreCase)
                                && string.Equals(i.ProductComingFrom, item.ProductComingFrom, StringComparison.CurrentCultureIgnoreCase)
                            )
                    .OrderBy(i => i.BoxCode)
                    .ThenBy(i => i.SpirePartNumber)
                    .ToList();

                var index = shipToItems.FindIndex(i => i.Id == item.Id);

                var show = index == 0;

                detail.Visible = show;

                if (show) {

                    detail.Visible = true;
                    if (shipToItems.Count > 1) {
                        var maxLabourHours = shipToItems.Max(i => i.LabourHours);
                        if (maxLabourHours == 0) {
                            maxLabourHours = 1;
                        }
                        var totalOrderQuantity = shipToItems.Sum(i => i.OrderQuantity);
                        var rawHours = totalOrderQuantity / maxLabourHours;
                        // matching javscript round, per https://stackoverflow.com/a/1863604
                        var shipToLabourHours = Math.Floor((rawHours > 0 && rawHours < 1 ? 1 : rawHours) + 0.5M);
                        txtLabourHours.Value = $"{shipToLabourHours:F0}h";
                    } else {
                        txtLabourHours.Value = $"{item.RoundedLabourHours}h";
                    }
                }

                if (!string.IsNullOrWhiteSpace(item.TariffCode)) {
                    txtContainerPickDescription.Text += $"\n{item.TariffCode}";
                }

                _formatIndex ++;
            }
        }
    }
    */
}
