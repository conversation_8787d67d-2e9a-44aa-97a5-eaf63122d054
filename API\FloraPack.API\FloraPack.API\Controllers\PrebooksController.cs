﻿using FloraPack.API.Reports.Prebooks;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.Prebooks;
using FloraPack.API.Repositories.Settings;
using FloraPack.API.Spire;
// using GrapeCity.ActiveReports.Export.Pdf.Section;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Prebooks")]
public class PrebooksController(IConfiguration configuration, PrebookRepository prebookRepository,
        PrebookEmailRepository prebookEmailRepository, SpireRepository spireRepository, PrebookReportFactory reportFactory,
        SettingsRepository settingsRepository)
    : FloraPackControllerBase(configuration)
{
    private readonly IConfiguration _configuration = configuration;

    [HttpGet("")]
    public async Task<IActionResult> Index([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {

        var prebooks = await prebookRepository.List(startDate, endDate);

        var response = new IndexResponse(prebooks);

        return Ok(response);
    }

    [HttpPost("Download")]
    public IActionResult Download([FromBody] PrebookListReportModel model)
    {
        var report = PrebookListReport.Create(model);

        Response.Headers.Append("Content-Disposition", "attachment;filename=Prebooks.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Items")]
    public async Task<IActionResult> ItemSummary([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null,
        [FromQuery] bool groupByCustomer = true, [FromQuery] bool groupByShipTo = true, [FromQuery] bool groupByVendor = true,
        [FromQuery] bool groupBySalesperson = true, [FromQuery] bool groupBySeason = true, [FromQuery] bool groupByIsBlanket = true, [FromQuery] string dateGrouping = "Date")
    {
        var items = await prebookRepository.ItemSummary(startDate, endDate,
            groupByCustomer, groupByShipTo, groupByVendor, groupBySalesperson, groupBySeason, groupByIsBlanket, dateGrouping);

        var response = new ItemSummaryResponse(items);

        return Ok(response);
    }

    [HttpGet("Items/Blanket")]
    public async Task<IActionResult> BlanketItemList([FromQuery] DateTime? startDate = null, [FromQuery] DateTime? endDate = null)
    {

        var blanketItems = await prebookRepository.BlanketList(startDate, endDate);

        var response = new BlanketItemListResponse(blanketItems);

        return Ok(response);
    }

    [HttpPost("Items/Blanket/Download")]
    public IActionResult BlanketItemDownload([FromBody] BlanketItemReportModel model)
    {

        var report = BlanketItemReport.Create(model);

        Response.Headers.Append("Content-Disposition", "attachment;filename=BlanketItems.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpPost("Items/Download")]
    public IActionResult ItemSummaryDownload([FromBody] ItemSummaryReportModel model)
    {
        var report = PrebookItemSummaryReport.Create(model);

        Response.Headers.Append("Content-Disposition", "attachment;filename=PrebookItems.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("{id:int}")]
    public async Task<IActionResult> Detail(int id)
    {
        var prebook = await prebookRepository.Detail(id);
        if (prebook == null) {
            return NotFound();
        }

        var emails = await prebookEmailRepository.GetEmailsForPrebook(id);

        var blanketItems = await prebookRepository.BlanketItems(prebook.Id);

        var response = new PrebookDetailResponse(prebook, emails, blanketItems);
        return Ok(response);
    }

    [HttpPost("")]
    public async Task<IActionResult> Create([FromBody] PrebookCreate prebook)
    {
        var who = await UserEmail();
        
        var id = await prebookRepository.Create(prebook, who);
        var url = Url.Action(nameof(Detail), new { id });
        if (url == null) {
            throw new ArgumentOutOfRangeException(nameof(prebook));
        }

        return Created(url, new { id });
    }

    [HttpPut("{id:int}")]
    public async Task<IActionResult> Update([FromBody] PrebookDetail model)
    {
        var who = await UserEmail();
        
        await prebookRepository.Update(model, who);

        var prebook = await prebookRepository.Detail(model.Id);
        if (prebook == null) {
            return NotFound();
        }

        var emails = await prebookEmailRepository.GetEmailsForPrebook(model.Id);
        var blanketItems = await prebookRepository.BlanketItems(prebook.Id);
        
        var response = new PrebookDetailResponse(prebook, emails, blanketItems);
        return Ok(response);
    }

    [HttpDelete("{id:int}")]
    public async Task<IActionResult> Delete(int id)
    {
        var who = await UserEmail();

        await prebookRepository.Delete(id, who!);

        return NoContent();
    }

    [HttpGet("Seasons")]
    public async Task<IActionResult> Seasons()
    {
        var seasons = await prebookRepository.GetSeasons();
        var response = new SeasonsResponse(seasons);

        return Ok(response);
    }

    [HttpPost("Seasons")]
    public async Task<IActionResult> CreateSeason([FromBody] CreateSeasonModel model)
    {
        await prebookRepository.CreateSeason(model.Name, model.SeasonDate);

        return NoContent();
    }

    [HttpGet("Defaults")]
    public async Task<IActionResult> Defaults(int spireInventoryId, int customerId, int shipToId)
    {
        var defaults = await settingsRepository.GetProductShipToDefault(spireInventoryId, shipToId);

        if (defaults.CustomerItemCode == null) {
            var customerDefaults = await settingsRepository.GetProductCustomerDefault(spireInventoryId, customerId);
            defaults.CustomerItemCode = customerDefaults.CustomerItemCode;
        }

        var response = new ProductShipToResponse(defaults);

        return Ok(response);
    }

    [HttpGet("{id:int}/Email/Pending")]
    public async Task<IActionResult> PendingEmail(int id)
    {
        var detail = await prebookRepository.Detail(id);
        if (detail == null) {
            return NotFound();
        }

        var to = new List<string>();

        if (detail.VendorId.HasValue) {
            var vendor = await spireRepository.GetVendorDetail(detail.VendorId.Value);
            if (vendor != null) {
                to.AddRange(vendor.GrowerPoEmailAddresses());
            }
        }

        var email = await prebookEmailRepository.FromPrebook(detail);

        if (email == null) {
            return NotFound();
        }

        var templates = await spireRepository.EmailTemplates("prebook");

        var response = new PendingEmailResponse(email, to, templates);

        return Ok(response);
    }

    [HttpPost("{id:int}/Email")]
    public async Task<IActionResult> CreateEmail(int id, [FromBody] CreateEmailModel model)
    {
        var from = await UserEmail();
        var detail = await prebookRepository.Detail(id);
        if (detail == null) {
            return NotFound();
        }

        var report = await reportFactory.CreateReport(id);
        if (report == null) {
            return NotFound();
        }

        // TODO: Replace with alternative PDF generation
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;
        // var reports = new List<EmailAttachment> {
        //     new (memStream.ToArray(), $"Prebook {id:0000}.pdf", "application/pdf")
        // };

        // Temporarily disable email with PDF attachment
        return BadRequest("Email with PDF attachment temporarily disabled - GrapeCity ActiveReports not available");

        // var attachmentsPath = Environment.ExpandEnvironmentVariables(_configuration["AttachmentsPath"]!);
        // var attachments = await prebookRepository.GetAttachmentsForPrebook(id);
        // var attachmentReports = new List<EmailAttachment>();
        // foreach (var upgradeAttachment in attachments) {
        //     var item = detail.Items.FirstOrDefault(i => i.Id == upgradeAttachment.PrebookItemId);
        //     var path = Path.Combine(attachmentsPath, upgradeAttachment.Filename);
        //     var filename = item?.SpirePartNumber ?? Path.GetFileNameWithoutExtension(upgradeAttachment.Filename);
        //     var extension = Path.GetExtension(upgradeAttachment.Filename);
        //     var attachmentName = $"{filename}{extension}";
        //     if (System.IO.File.Exists(path)) {
        //         var bytes = await System.IO.File.ReadAllBytesAsync(path);
        //         attachmentReports.Add(new(bytes, attachmentName, upgradeAttachment.FileType));
        //     }
        // }

        // reports.AddRange(attachmentReports);

        // var htmlBody = model.Body.Replace("\n", "<br />");
        // var to = model.To.Split(";").Select(to => to.Trim());
        // var cc = model.Cc?.Split(";").Select(t => t.Trim()).ToList() ?? new();

        // var bcc = model.Bcc?.Split(";").Select(t => t.Trim());
        // await SendMail(model.Subject, htmlBody, to, cc, bcc, reports);

        var prebookEmail = await prebookEmailRepository.Create(detail, model.To, model.Cc, model.Bcc, model.Subject, model.Body, from ?? "");
        if (prebookEmail == null) {
            return NotFound();
        }

        await prebookRepository.Unconfirm(id, from ?? "Unknown");

        if (detail.VendorId == Constants.UpgradesVendorId || detail.Items.Any(i => i.UpgradeSheet)) {
            var upgradeReport = await reportFactory.CreateUpgradeSheetReport(detail, prebookEmail.Id);
            if (upgradeReport != null) {
                // TODO: Replace with alternative PDF generation
                // upgradeReport.Run();
                // var upgradeExport = new PdfExport();
                // var upgradeStream = new MemoryStream();
                // upgradeExport.Export(upgradeReport.Document, upgradeStream);
                // upgradeStream.Position = 0;
                // var upgradeReports = new List<EmailAttachment> {
                //     new(upgradeStream.ToArray(), $"Prebook {id:0000}.pdf", "application/pdf")
                // };

                // Temporarily disable upgrade report email
                return BadRequest("Upgrade report email temporarily disabled - GrapeCity ActiveReports not available");

// #if DEBUG
//                 var upgradeTo = new List<string> { "<EMAIL>" };
// #else
//                 var upgradeTo = await spireRepository.GetUpgradeVendorEmailAddresses();
// #endif
//                 if (upgradeTo.Any()) {
//                     var upgradesCc = new List<string>();
//                     if (!string.IsNullOrWhiteSpace(model.UpgradesCc)) {
//                         model.UpgradesCc
//                             .Split(";")
//                             .Select(c => c.Trim())
//                             .Where(c => !upgradeTo.Contains(c))
//                             .ToList()
//                             .ForEach(upgradesCc.Add);
//                     }
//                     upgradeReports.AddRange(attachmentReports);

//                     var upgradesHtmlBody = model.UpgradesBody.Replace("\n", "<br />");
//                     await SendMail($"[UPGRADE SHEET NOTIFICATION]: {model.Subject}", upgradesHtmlBody, upgradeTo, upgradesCc, reports: upgradeReports);
//                 }
            }
        }

        var response = new CreateEmailResponse(prebookEmail);
        return Ok(response);
    }

    [HttpGet("Email/Pending/{vendorId:int}")]
    public async Task<IActionResult> PendingBatchEmail(int vendorId)
    {
        var to = new List<string>();
        var vendor = await spireRepository.GetVendorDetail(vendorId);
        if (vendor != null) {
            to.AddRange(vendor.GrowerPoEmailAddresses());
        }

        var response = new PendingBatchEmailResponse(to);

        return Ok(response);
    }

    [HttpPost("Emails")]
    public async Task<IActionResult> CreateBatchEmail([FromBody] CreateBatchEmailModel model)
    {
        var from = await UserEmail();

        var reports = new List<EmailAttachment>();

        foreach (var id in model.PrebookIds) {
            var detail = await prebookRepository.Detail(id);
            if (detail != null) {

                // TODO: Replace with alternative PDF generation
                // var report = await reportFactory.CreateReport(id);
                // if (report != null) {
                //     report.Run();
                //     var export = new PdfExport();
                //     var memStream = new MemoryStream();
                //     export.Export(report.Document, memStream);
                //     memStream.Position = 0;
                //     reports.Add(new(memStream.ToArray(), $"Prebook {id:0000}.pdf", "application/pdf"));
                // }

                // Temporarily disable PDF generation for batch emails
                return BadRequest("Batch email with PDF attachments temporarily disabled - GrapeCity ActiveReports not available");

                // await prebookEmailRepository.Create(detail, model.To, model.Cc, model.Bcc,
                //     model.Subject, model.Body, from ?? "");

                // await prebookRepository.Unconfirm(id, from ?? "");
            }
        }

        // var htmlBody = model.Body.Replace("\n", "<br />");
        // var to = model.To.Split(";").Select(t => t.Trim());
        // var cc = model.Cc?.Split(";").Select(t => t.Trim());
        // var bcc = model.Bcc?.Split(";").Select(t => t.Trim());
        // await SendMail(model.Subject, htmlBody, to, cc, bcc, reports);

        return Ok();
    }

    [HttpPost("{id:int}/Spire")]
    public async Task<IActionResult> CreatePurchaseOrder(int id)
    {
        var prebook = await prebookRepository.Detail(id);
        if (prebook == null) {
            return NotFound();
        }

        var po = await spireRepository.CreatePurchaseOrder(prebook);
        if (po == null) {
            throw new ApplicationException("Spire Purchase Order not created.");
        }

        await prebookRepository.SetSpirePurchaseOrder(id, po.Id, po.Number);

        var response = new CreatePurchaseOrderResponse(po.Id, po.Number);
        return Ok(response);
    }

    [HttpPost("{id:int}/Confirm")]
    public async Task<IActionResult> Confirm(int id)
    {
        var who = await UserEmail() ?? string.Empty;
        await prebookRepository.Confirm(id, who);

        var prebook = await prebookRepository.Detail(id);
        if (prebook == null) {
            return NotFound();
        }

        var response = new PrebookConfirmationResponse(prebook);
        return Ok(response);
    }

    [HttpGet("BlanketItems")]
    public async Task<IActionResult> BlanketItems([FromQuery] int? prebookId = null)
    {
        var items = await prebookRepository.BlanketItems(prebookId);
        var response = new BlanketItemsResponse(items);
        return Ok(response);
    }

    [HttpPost("Items/{id:int}/Upgrades/Confirm")]
    public async Task<IActionResult> ConfirmUpgradeItem(int id)
    {
        var who = await UserEmail();
        await prebookRepository.ConfirmUpgradeItem(id, who);
        return Ok();
    }

    [HttpPost("Over-And-Above")]
    public async Task<IActionResult> OverAndAbove([FromBody] OverAndAboveModel model)
    {
        var report = await reportFactory.OverAndAboveReport(model.StartDate, model.EndDate);

        Response.Headers.Append("Content-Disposition", "attachment;filename=OverAndAbove.xslx");
        return File(report, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    }

    [HttpGet("Attachments/{id:int}")]
    public async Task<IActionResult> Attachments(int id)
    {
        var attachments = await prebookRepository.GetAttachmentsForPrebook(id);
        var response = new AttachmentsResponse(attachments);
        return Ok(response);
    }

    [HttpPost("Attachments")]
    public async Task<IActionResult> AddAttachment([FromForm] AddAttachmentModel model)
    {
        if (model.File == null || model.File.Length == 0) {
            return BadRequest("The Image was empty.");
        }

        var attachmentPath = _configuration["AttachmentsPath"]!;

        var extension = Path.GetExtension(model.File.FileName);
        var filename = Path.ChangeExtension(Path.GetRandomFileName(), extension);
        var filePath = Path.Join(attachmentPath, filename);

        await using var stream = System.IO.File.Create(filePath);
        await model.File.CopyToAsync(stream);

        var created = await prebookRepository.CreateAttachment(filename, model.FileType, model.PrebookItemIds);
        var response = new AddAttachmentResponse(created);

        return Ok(response);
    }

    [HttpDelete("Attachments/{id:int}")]
    public async Task<IActionResult> DeleteAttachment(int id)
    {
        var attachment = await prebookRepository.GetAttachment(id);

        if (attachment != null) {
            try {

                var attachmentPath = _configuration["AttachmentsPath"]!;
                var path = Path.Combine(attachmentPath, attachment.Filename);
                System.IO.File.Delete(path);

            } catch (Exception) { /* don't worry about it */ }
        }

        await prebookRepository.DeleteAttachment(id);
        return Ok();
    }

    private record IndexResponse(IEnumerable<PrebookListItem> Prebooks);

    private record ItemSummaryResponse(IEnumerable<PrebookSummaryItem> Items);

    private record BlanketItemListResponse(IEnumerable<BlanketItemListItem> Items);

    public class PrebookListReportModel
    {
        public List<PrebookListItem> Items { get; set; } = new();
    }

    public class ItemSummaryReportModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool GroupByCustomer { get; set; }
        public bool GroupByShipTo { get; set; }
        public bool GroupByVendor { get; set; }
        public bool GroupBySalesperson { get; set; }
        public bool GroupBySeason { get; set; }
        public bool GroupByIsBlanket { get; set; }
        public string? DateGrouping { get; set; }
        public List<PrebookSummaryItem> Items { get; set; } = new();
    }

    public class BlanketItemReportModel
    {
        public List<BlanketItemListItem> BlanketItems { get; set; } = new();
    }

    private record PrebookDetailResponse(PrebookDetail Prebook, IEnumerable<PrebookEmail> Emails, IEnumerable<PrebookBlanketItem> BlanketItems);

    private record SeasonsResponse(IEnumerable<Season> Seasons);

    public class CreateSeasonModel
    {
        public string Name { get; set; } = string.Empty;
        public DateTime SeasonDate { get; set; }
    }

    private record ProductShipToResponse(ProductShipToDefault Default);

    private record PendingEmailResponse(PendingPrebookEmail Prebook, IEnumerable<string> ToAddresses, IEnumerable<EmailTemplate> Templates);

    public class CreateEmailModel
    {
        public string To { get; set; } = string.Empty;
        public string? Cc { get; set; }
        public string? Bcc { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public string UpgradesBody { get; set; } = string.Empty;
        public string? UpgradesCc { get; set; }
    }

    private record CreateEmailResponse(PrebookEmail Email);

    public class CreateBatchEmailModel
    {
        public string To { get; set; } = string.Empty;
        public string? Cc { get; set; }
        public string? Bcc { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public List<int> PrebookIds { get; set; } = new();
    }

    private record PendingBatchEmailResponse(IEnumerable<string> ToAddresses);

    private record CreatePurchaseOrderResponse(int SpirePurchaseOrderId, string SpirePurchaseOrderNumber);

    private record PrebookConfirmationResponse(PrebookDetail Prebook);

    public record PlantModel(string Id, string Name, string Abbreviation);

    private record BlanketItemsResponse(IEnumerable<PrebookBlanketItem> BlanketItems);

    public class OverAndAboveModel
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class AddAttachmentModel
    {
        public IFormFile? File { get; set; }
        public string FileType { get; set; } = string.Empty;
        public List<int> PrebookItemIds { get; set; } = new();
    }

    private record AddAttachmentResponse(IEnumerable<PrebookItemAttachment> Attachments);

    private record AttachmentsResponse(IEnumerable<PrebookItemAttachment> Attachments);
}