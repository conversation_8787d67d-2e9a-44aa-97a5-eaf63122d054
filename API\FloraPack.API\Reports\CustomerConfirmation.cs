using System;
using System.Collections.Generic;

namespace FloraPack.API.Reports
{
    public partial class CustomerConfirmation : GrapeCity.ActiveReports.SectionReport
    {
        public CustomerConfirmation(FutureOrderSummaryReportFutureOrder futureOrder, List<FutureOrderSummaryReportFutureOrderItem> items)
        {
            InitializeComponent();

            txtTotalPrice.Visible = futureOrder.TotalPrice > 0;
            
            lblShipmentDetails.Visible = !string.IsNullOrWhiteSpace(futureOrder.SpireNotes);
            txtShipmentDetails.Visible = !string.IsNullOrWhiteSpace(futureOrder.SpireNotes);

            DataSource = new List<FutureOrderSummaryReportFutureOrder> { futureOrder };

            srptItems.Report = new CustomerConfirmationItem { DataSource = items };
        }
    }
}
