using System;
using System.Collections.Generic;
using GrapeCity.ActiveReports.Document.Drawing;

namespace FloraPack.API.Reports
{
    public partial class Prebook : GrapeCity.ActiveReports.SectionReport
    {
        public Prebook(PrebookReportPrebook prebook, List<PrebookReportPrebookItem> items)
        {
            InitializeComponent();

            if (prebook.IsBlanket) {
                lblTitle.Text = "BLANKET PREBOOK";
            }

            var revisionDate = DateTime.Now.ToString("ddd MMM d, yyyy");
            if (prebook.PreviousRevisionDate.HasValue) {
                revisionDate += $" replaces Prebook sent {prebook.PreviousRevisionDate.Value:ddd MMM d, yyyy}";
            }

            infoPrintDate.Text = revisionDate;

            var salesperson = string.IsNullOrWhiteSpace(prebook.SalespersonName) ? "your sales rep" : prebook.SalespersonName;
            txtSalesperson.Text = $"Please contact {salesperson} with any questions.";

            lblRevised.Visible = prebook.HasRevision;


            var requiredDateHtml = "<html><body style='margin: 0; padding: 0 0 10px 0;'>";
            if (string.IsNullOrWhiteSpace(prebook.RequiredDateFormatted) && string.IsNullOrWhiteSpace(prebook.PreviousRequiredDateFormatted)) {
                lblRequiredDate.Visible = false;
                txtRequiredDate.Visible = false;
            } else {


                if (!string.IsNullOrWhiteSpace(prebook.PreviousRequiredDateFormatted) &&
                    prebook.RequiredDateFormatted != prebook.PreviousRequiredDateFormattedWithSeasonName) {

                    requiredDateHtml += $"<div style='text-decoration: line-through; font-family: Calibri; font-size: 10pt;'>{prebook.PreviousRequiredDateFormatted.Replace("\n", "<br />")}</div>";
                }

                var requiredDateBackground = BackgroundColour(prebook.MostRecentRequiredDateFormatted != null &&
                                                              prebook.MostRecentRequiredDateFormatted !=
                                                              prebook.RequiredDateFormatted);
                requiredDateHtml += $"<div style='font-weight: bold; font-family: Calibri; font-size: 10pt; {requiredDateBackground}'>{prebook.RequiredDateFormatted.Replace("\n", "<br />")}</div>";
            }
            requiredDateHtml += "</body></html>";
            txtRequiredDate.Html = requiredDateHtml;

            var commentsHtml = "<html><body style='margin: 0; padding: 0 0 10px 0;'>";
            if (string.IsNullOrWhiteSpace(prebook.PreviousComments) && string.IsNullOrWhiteSpace(prebook.Comments)) {
                lblComments.Visible = false;
                txtComments.Visible = false;
            } else {

                var commentsBold = false;

                if (!string.IsNullOrWhiteSpace(prebook.PreviousComments) && prebook.PreviousComments != prebook.Comments) {
                    commentsHtml += $"<div style='text-decoration: line-through; font-family: Calibri; font-size: 10pt;'>{prebook.PreviousComments.Replace("\n", "<br />")}</div>";
                    commentsBold = true;
                }

                var boldComments = commentsBold ? "font-weight: bold; " : "";
                var comments = (prebook.Comments ?? string.Empty).Replace("\n", "<br />");
                var commentsBackground = BackgroundColour(prebook.MostRecentComments != null && prebook.MostRecentComments != prebook.Comments);
                commentsHtml += $"<div style='{boldComments}font-family: Calibri; font-size: 10pt; {commentsBackground}'>{comments}</div>";

            }
            commentsHtml += "</body></html>";

            txtComments.Html = commentsHtml;

            DataSource = new List<PrebookReportPrebook> { prebook };

            srptItems.Report = new PrebookItem { DataSource = items };
        }

        private static string BackgroundColour(bool changed) => changed ? "background-color: #ffff00;" : "";
    }

    public class PrebookReportPrebook
    {
        public int Id { get; set; }
        public bool IsBlanket { get; set; }
        public DateTime? RequiredDate { get; set; }
        public DateTime? BlanketStartDate { get; set; }
        public DateTime? PreviousRevisionDate { get; set; }
        public string SeasonName { get; set; } = string.Empty;
        public string VendorName { get; set; } = string.Empty;
        public string SalespersonName { get; set; } = string.Empty;
        public string Comments { get; set; } = string.Empty;
        public bool HasRevision { get; set; }
        public DateTime? PreviousRequiredDate { get; set; }
        public DateTime? MostRecentRequiredDate { get; set; }
        public string PreviousComments { get; set; } = string.Empty;
        public string? MostRecentComments { get; set; }

        public string RequiredDateFormatted
        {
            get
            {
                var value = string.Empty;
                if (RequiredDate.HasValue) {
                    value = RequiredDate.Value.ToString("MMM d, yyyy");
                }

                if (BlanketStartDate.HasValue) {
                    var year = BlanketStartDate.Value.Year == RequiredDate.GetValueOrDefault(BlanketStartDate.Value).Year ? string.Empty : $", {BlanketStartDate.Value.Year}";
                    value = $"{BlanketStartDate.Value:MMM d}{year} - {value}";
                }

                var originalValue = value;
                if (!string.IsNullOrWhiteSpace(SeasonName)) {
                    if (!string.IsNullOrWhiteSpace(originalValue)) {
                        value += "\n(";
                    }

                    value += SeasonName;
                    if (!string.IsNullOrWhiteSpace(originalValue)) {
                        value += ")";
                    }
                }

                return value;
            }
        }

        public string PreviousRequiredDateFormattedWithSeasonName
        {
            get
            {
                var value = PreviousRequiredDateFormatted;

                if (BlanketStartDate.HasValue) {
                    var year = BlanketStartDate.Value.Year == RequiredDate.GetValueOrDefault(BlanketStartDate.Value).Year ? string.Empty : $", {BlanketStartDate.Value.Year}";
                    value = $"{BlanketStartDate.Value:MMM d}{year} - {value}";
                }

                if (!string.IsNullOrWhiteSpace(SeasonName)) {
                    if (!string.IsNullOrWhiteSpace(value)) {
                        value += "\n(";
                    }

                    value += SeasonName;
                    if (!string.IsNullOrWhiteSpace(value)) {
                        value += ")";
                    }
                }

                return value;
            }
        }

        public string PreviousRequiredDateFormatted
        {
            get
            {
                var value = string.Empty;
                if (PreviousRequiredDate.HasValue) {
                    value = PreviousRequiredDate.Value.ToString("MMM d, yyyy");

                    if (BlanketStartDate.HasValue) {
                        var year = BlanketStartDate.Value.Year == PreviousRequiredDate.GetValueOrDefault(BlanketStartDate.Value).Year ? string.Empty : $", {BlanketStartDate.Value.Year}";
                        value = $"{BlanketStartDate.Value:MMM d}{year} - {value}";
                    }
                }

                return value;
            }
        }

        public string? MostRecentRequiredDateFormatted
        {
            get
            {
                if (MostRecentRequiredDate == null) {
                    return null;
                }


                var value = MostRecentRequiredDate.Value.ToString("MMM d, yyyy");

                if (BlanketStartDate.HasValue) {
                    var year = BlanketStartDate.Value.Year ==
                               PreviousRequiredDate.GetValueOrDefault(BlanketStartDate.Value).Year
                        ? string.Empty
                        : $", {BlanketStartDate.Value.Year}";
                    value = $"{BlanketStartDate.Value:MMM d}{year} - {value}";
                }

                if (BlanketStartDate.HasValue) {
                    var year = BlanketStartDate.Value.Year ==
                               RequiredDate.GetValueOrDefault(BlanketStartDate.Value).Year
                        ? string.Empty
                        : $", {BlanketStartDate.Value.Year}";
                    value = $"{BlanketStartDate.Value:MMM d}{year} - {value}";
                }

                if (!string.IsNullOrWhiteSpace(SeasonName)) {
                    if (!string.IsNullOrWhiteSpace(value)) {
                        value += "\n(";
                    }

                    value += SeasonName;
                    if (!string.IsNullOrWhiteSpace(value)) {
                        value += ")";
                    }
                }

                return value;
            }
        }
    }
}
