﻿using ClosedXML.Excel;
using FloraPack.API.Repositories.Boekestyn;
using FloraPack.API.Repositories.FutureOrders;
using FloraPack.API.Repositories.Prebooks;

namespace FloraPack.API.Reports.Prebooks;

public class PrebookReportFactory(PrebookRepository prebookRepository, PrebookEmailRepository prebookEmailRepository, FutureOrderRepository futureOrderRepository)
{
    public async Task<Prebook?> CreateReport(int id)
    {
        var prebook = await prebookRepository.Detail(id);
        if (prebook == null) {
            return null;
        }

        var futureOrders = await futureOrderRepository.FutureOrdersForPrebook(id);

        var emails = (await prebookEmailRepository.GetEmailsForPrebook(id)).OrderByDescending(p => p.Created).ToList();
        var revised = emails.Any();

        var prebookEmail = await prebookEmailRepository.FromPrebook(prebook);
        if (prebookEmail == null) {
            return null;
        }

        var mostRecent = emails.FirstOrDefault();

        var items = prebookEmail.Items.Aggregate(new List<PrebookReportPrebookItem>(), (list, i) => {
            var previous = emails.Aggregate(new List<PrebookEmailItem>(), (emailList, e) => {
                emailList.AddRange(e.Items.Where(pi => pi.PrebookItemId == i.PrebookItemId));
                return emailList;
            });
            var mostRecentEmailItem = mostRecent?.Items.FirstOrDefault(pi => pi.PrebookItemId == i.PrebookItemId);
            var prebookItem = prebook.Items.FirstOrDefault(pi => pi.Id == i.PrebookItemId);
            FutureOrderDetail? futureOrder = default;
            FutureOrderDetailItem? futureOrderItem = default;
            foreach (var fo in futureOrders) {
                futureOrderItem = fo.Items.FirstOrDefault(foi => foi.Id == prebookItem?.FutureOrderItemId);
                if (futureOrderItem != null) {
                    futureOrder = fo;
                    break;
                }
            }

            var existingIdentical = list.FirstOrDefault(i2 =>
                i2.PackQuantity == i.PackQuantity &&
                i2.Description == i.Description &&
                i2.PotCover == (i.PotCover ?? string.Empty) &&
                i2.BoxCode == (futureOrder?.BoxCode ?? i.BoxCode ?? string.Empty)  &&
                i2.UPC == (i.Upc ?? string.Empty) &&
                i2.Retail == (i.Retail ?? string.Empty) &&
                i2.WeightsAndMeasures == i.WeightsAndMeasures &&
                i2.Comments == (futureOrderItem?.Comments ?? i.Comments ?? string.Empty) &&
                i2.BlanketWeekId == (prebookItem?.BlanketWeekId ?? string.Empty) &&
                i2.SpecialPrice == prebookItem?.SpecialPrice &&
                i2.UpgradeSheet == (prebookItem?.UpgradeSheet ?? false) &&
                i2.GrowerItemNotes == MakeGrowerItemNotes(futureOrder?.GrowerItemNotes ?? prebook.GrowerItemNotes, futureOrderItem?.GrowerItemNotes ?? prebookItem?.GrowerItemNotes)
            );

            if (existingIdentical != null) {
                existingIdentical.OrderQuantity += i.OrderQuantity;
            } else {
                var sortOrder = prebook.Items
                    .Where(pi =>
                        pi.Id == i.PrebookItemId ||
                        (pi.SpirePartNumber == i.SpirePartNumber && pi.FutureOrderId != futureOrder?.Id))
                    .Select(pi => futureOrders.FirstOrDefault(f => f.Items.Any(fi => fi.Id == pi.FutureOrderItemId))?.Items.FirstOrDefault(fi => fi.Id == pi.FutureOrderItemId))
                    .MinBy(fi => (fi?.SortOrder).GetValueOrDefault(1))?.SortOrder ?? int.MaxValue;
                var growerItemNotes = MakeGrowerItemNotes(futureOrder?.GrowerItemNotes ?? prebook.GrowerItemNotes, futureOrderItem?.GrowerItemNotes ?? prebookItem?.GrowerItemNotes);
                var item = new PrebookReportPrebookItem {
                    SortOrder = sortOrder,
                    HasRevision = revised,
                    ItemWasRevised = emails.Any(e => e.Items.Any(item => item.SpirePartNumber == i.SpirePartNumber)),
                    OrderQuantity = i.OrderQuantity,
                    PreviousOrderQuantity = previous.FirstOrDefault(p => p.OrderQuantity != i.OrderQuantity)?.OrderQuantity ?? previous.FirstOrDefault()?.OrderQuantity,
                    MostRecentOrderQuantity = mostRecentEmailItem?.OrderQuantity,
                    IsApproximate = prebookItem?.IsApproximate == true,
                    PackQuantity = i.PackQuantity,
                    PreviousPackQuantity = previous.FirstOrDefault(p => p.OrderQuantity != i.OrderQuantity)?.PackQuantity ?? previous.FirstOrDefault()?.PackQuantity,
                    Description = i.Description,
                    PreviousDescription = previous.FirstOrDefault(p => p.Description != i.Description)?.Description ?? previous.FirstOrDefault()?.Description ?? string.Empty,
                    MostRecentDescription = mostRecentEmailItem?.Description,
                    HasPotCover = !string.IsNullOrWhiteSpace(i.PotCover),
                    PotCover = i.PotCover ?? string.Empty,
                    PreviousHasPotCover = previous.Any(p => !string.IsNullOrWhiteSpace(p.PotCover)),
                    PreviousPotCover = FindPreviousValue(previous, (p) => p?.PotCover, i.PotCover),
                    MostRecentPotCover = mostRecentEmailItem?.PotCover,
                    BoxCode = futureOrder?.BoxCode ?? i.BoxCode ?? string.Empty,
                    PreviousBoxCode = FindPreviousValue(previous, p => p?.BoxCode, futureOrder?.BoxCode ?? i.BoxCode),
                    MostRecentBoxCode = mostRecentEmailItem?.BoxCode,
                    DateCode = i.DateCode ?? string.Empty,
                    PreviousDateCode = FindPreviousValue(previous, p => p?.DateCode, i.DateCode),
                    MostRecentDateCode = mostRecentEmailItem?.DateCode,
                    UPC = i.Upc ?? string.Empty,
                    PreviousUPC = FindPreviousValue(previous, p => p?.Upc, i.Upc),
                    MostRecentUPC = mostRecentEmailItem?.Upc,
                    Retail = i.Retail ?? string.Empty,
                    PreviousRetail = FindPreviousValue(previous, p => p?.Retail, i.Retail),
                    MostRecentRetail = mostRecentEmailItem?.Retail,
                    WeightsAndMeasures = i.WeightsAndMeasures,
                    Comments = futureOrderItem?.Comments ?? i.Comments ?? string.Empty,
                    GrowerItemNotes = growerItemNotes,
                    PreviousComments = FindPreviousValue(previous, p => p?.Comments, futureOrderItem?.Comments ?? i.Comments),
                    MostRecentComments = mostRecentEmailItem?.Comments,
                    BlanketWeekId = prebookItem?.BlanketWeekId ?? string.Empty,
                    SpecialPrice = prebookItem?.SpecialPrice,
                    UpgradeSheet = prebookItem?.UpgradeSheet ?? false,
                    PreviousUpgradeSheet = previous.Any(p => p.UpgradeSheet)
                };
                list.Add(item);
            }


            return list;
        })
        .OrderBy(i => i.SortOrder)
        .ToList();

        emails.Aggregate(new List<PrebookEmailItem>(), (list, e) => {
                e.Items
                    .Where(i => i.PrebookItemId == null && list.All(i2 => i2.SpirePartNumber != i.SpirePartNumber))
                    .ToList()
                    .ForEach(list.Add);
                return list;
            })
            .Select(i => {
                var mostRecentEmailItem = mostRecent?.Items.FirstOrDefault(pi => pi.PrebookItemId == i.PrebookItemId);
                var prebookItem = prebook.Items.FirstOrDefault(pi => pi.Id == i.PrebookItemId);

                return new PrebookReportPrebookItem {
                    HasRevision = revised,
                    ItemWasRevised = true,
                    OrderQuantity = 0,
                    PreviousOrderQuantity = i.OrderQuantity,
                    MostRecentOrderQuantity = mostRecentEmailItem?.OrderQuantity,
                    PackQuantity = i.PackQuantity,
                    Description = i.Description,
                    HasPotCover = false,
                    PotCover = string.Empty,
                    BoxCode = string.Empty,
                    DateCode = string.Empty,
                    UPC = string.Empty,
                    PreviousUPC = i.Upc ?? string.Empty,
                    Retail = string.Empty,
                    WeightsAndMeasures = i.WeightsAndMeasures,
                    Comments = string.Empty,
                    IsApproximate = prebookItem?.IsApproximate == true
                };
            })
            .ToList()
            .ForEach(items.Add);

        var reportPrebook = new PrebookReportPrebook {
            Id = prebook.Id,
            IsBlanket = prebook.IsBlanket,
            HasRevision = revised,
            RequiredDate = prebook.RequiredDate,
            MostRecentRequiredDate = mostRecent?.RequiredDate,
            BlanketStartDate = prebook.BlanketStartDate,
            PreviousRevisionDate = emails.FirstOrDefault()?.Created,
            SeasonName = prebook.SeasonName ?? string.Empty,
            VendorName = prebook.VendorName ?? string.Empty,
            SalespersonName = prebook.SalespersonName ?? string.Empty,
            Comments = prebook.Comments ?? string.Empty,
            PreviousRequiredDate = emails.FirstOrDefault(e => e.RequiredDate != prebook.RequiredDate)?.RequiredDate ?? emails.FirstOrDefault()?.RequiredDate,
            PreviousComments = emails.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments) && e.Comments != prebook.Comments)?.Comments ?? 
                               emails.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments))?.Comments ?? 
                               string.Empty,
            MostRecentComments = mostRecent?.Comments
        };

        var report = new Prebook(reportPrebook, items);
        return report;
    }

    public async Task<Prebook?> CreateUpgradeSheetReport(PrebookDetail prebook, int currentEmailId)
    {
        var emails = (await prebookEmailRepository.GetEmailsForPrebook(prebook.Id)).OrderBy(e => e.Created).ToList();
        var currentIndex = emails.FindIndex(e => e.Id == currentEmailId);
        if (currentIndex == -1) {
            return null;
        }

        var previousRevisions = emails.Where((_, i) => i < currentIndex).OrderByDescending(e => e.Created).ToList();
        var revised = previousRevisions.Any();

        var prebookEmail = emails[currentIndex];
        var mostRecent = previousRevisions.FirstOrDefault();

        var futureOrders = await futureOrderRepository.FutureOrdersForPrebook(prebook.Id);

        var items = prebookEmail.Items
            .Where(i => prebook.VendorId == Constants.UpgradesVendorId || prebook.Items.FirstOrDefault(pi => pi.Id == i.PrebookItemId)?.UpgradeSheet == true)
            .Select(i => {
                var prebookItem = prebook.Items.FirstOrDefault(pi => pi.Id == i.PrebookItemId);
                var previous = previousRevisions.Aggregate(new List<PrebookEmailItem>(), (list, e) => {
                    list.AddRange(e.Items.Where(pi => pi.PrebookItemId == i.PrebookItemId));
                    return list;
                });
                var mostRecentEmailItem = mostRecent?.Items.FirstOrDefault(pi => pi.PrebookItemId == i.PrebookItemId);

                FutureOrderDetail? futureOrder = default;
                FutureOrderDetailItem? futureOrderItem = default;
                foreach (var fo in futureOrders) {
                    futureOrderItem = fo.Items.FirstOrDefault(foi => foi.Id == prebookItem?.FutureOrderItemId);
                    if (futureOrderItem != null) {
                        futureOrder = fo;
                        break;
                    }
                }

                var growerItemNotes = MakeGrowerItemNotes(futureOrder?.GrowerItemNotes ?? prebook.GrowerItemNotes, futureOrderItem?.GrowerItemNotes ?? prebookItem?.GrowerItemNotes);

                var sortOrder = prebook.Items
                    .Where(pi =>
                        pi.Id == i.PrebookItemId ||
                        (pi.SpirePartNumber == i.SpirePartNumber && pi.FutureOrderId != futureOrder?.Id))
                    .Select(pi => futureOrders.FirstOrDefault(f => f.Items.Any(fi => fi.Id == pi.FutureOrderItemId))?.Items.FirstOrDefault(fi => fi.Id == pi.FutureOrderItemId))
                    .MinBy(fi => (fi?.SortOrder).GetValueOrDefault(1))?.SortOrder ?? int.MaxValue;

                return new PrebookReportPrebookItem {
                    SortOrder = sortOrder,
                    HasRevision = revised,
                    ItemWasRevised = previousRevisions.Any(e => e.Items.Any(item => item.SpirePartNumber == i.SpirePartNumber)),
                    OrderQuantity = i.OrderQuantity,
                    PreviousOrderQuantity = previous.FirstOrDefault(p => p.OrderQuantity != i.OrderQuantity)?.OrderQuantity ?? previous.FirstOrDefault()?.OrderQuantity,
                    MostRecentOrderQuantity = mostRecentEmailItem?.OrderQuantity,
                    IsApproximate = prebookItem?.IsApproximate == true,
                    PackQuantity = i.PackQuantity,
                    PreviousPackQuantity = previous.FirstOrDefault(p => p.PackQuantity != i.PackQuantity)?.PackQuantity ?? previous.FirstOrDefault()?.PackQuantity,
                    MostRecentPackQuantity = mostRecentEmailItem?.PackQuantity,
                    Description = i.Description,
                    PreviousDescription = previous.FirstOrDefault(p => p.Description != i.Description)?.Description ?? previous.FirstOrDefault()?.Description ?? string.Empty,
                    MostRecentDescription = mostRecentEmailItem?.Description,
                    HasPotCover = !string.IsNullOrWhiteSpace(i.PotCover),
                    PotCover = i.PotCover ?? string.Empty,
                    PreviousHasPotCover = previous.Any(p => !string.IsNullOrWhiteSpace(p.PotCover)),
                    PreviousPotCover = FindPreviousValue(previous, (p) => p?.PotCover, i.PotCover),
                    MostRecentPotCover = mostRecentEmailItem?.PotCover,
                    BoxCode = i.BoxCode ?? string.Empty,
                    PreviousBoxCode = FindPreviousValue(previous, p => p?.BoxCode, i.BoxCode),
                    MostRecentBoxCode = mostRecentEmailItem?.BoxCode,
                    DateCode = i.DateCode ?? string.Empty,
                    PreviousDateCode = FindPreviousValue(previous, p => p?.DateCode, i.DateCode),
                    MostRecentDateCode = mostRecentEmailItem?.DateCode,
                    UPC = i.Upc ?? string.Empty,
                    PreviousUPC = FindPreviousValue(previous, p => p?.Upc, i.Upc),
                    MostRecentUPC = mostRecentEmailItem?.Upc,
                    Retail = i.Retail ?? string.Empty,
                    PreviousRetail = FindPreviousValue(previous, p => p?.Retail, i.Retail),
                    MostRecentRetail = mostRecentEmailItem?.Retail,
                    WeightsAndMeasures = i.WeightsAndMeasures,
                    Comments = i.Comments ?? string.Empty,
                    GrowerItemNotes = growerItemNotes,
                    PreviousComments = FindPreviousValue(previous, p => p?.Comments, i.Comments),
                    MostRecentComments = mostRecentEmailItem?.Comments,
                    BlanketWeekId = prebookItem?.BlanketWeekId ?? string.Empty,
                    SpecialPrice = prebookItem?.SpecialPrice,
                    UpgradeSheet = prebookItem?.UpgradeSheet ?? false,
                    PreviousUpgradeSheet = previous.Any(p => p.UpgradeSheet),
                    MostRecentUpgradeSheet = mostRecentEmailItem?.UpgradeSheet
                };
            }).ToList();

        // if there haven't been any actual changes to the upgrade items, just forget it
        if (prebookEmail.RequiredDate == mostRecent?.RequiredDate && !items.Any(i => 
                            i.UpgradeSheet && i.MostRecentUpgradeSheet != true ||
                            i.OrderQuantity != i.MostRecentOrderQuantity.GetValueOrDefault(0) ||
                            i.PackQuantity != i.MostRecentPackQuantity.GetValueOrDefault(0) ||
                            i.Description != (i.MostRecentDescription ?? string.Empty) ||
                            i.PotCover != (i.MostRecentPotCover ?? string.Empty) ||
                            i.BoxCode != (i.MostRecentBoxCode ?? string.Empty) ||
                            i.DateCode != (i.MostRecentDateCode ?? string.Empty) ||
                            i.UPC != (i.MostRecentUPC ?? string.Empty) ||
                            i.Retail != (i.MostRecentRetail ?? string.Empty) ||
                            i.Comments != (i.MostRecentComments ?? string.Empty))) {
            return null;
        }

        var reportPrebook = new PrebookReportPrebook {
            Id = prebook.Id,
            IsBlanket = prebook.IsBlanket,
            HasRevision = revised,
            RequiredDate = prebook.RequiredDate,
            BlanketStartDate = prebook.BlanketStartDate,
            PreviousRevisionDate = previousRevisions.FirstOrDefault()?.Created,
            SeasonName = prebook.SeasonName ?? string.Empty,
            VendorName = prebook.VendorName ?? string.Empty,
            SalespersonName = prebook.SalespersonName ?? string.Empty,
            Comments = prebook.Comments ?? string.Empty,
            PreviousRequiredDate = previousRevisions.FirstOrDefault(e => e.RequiredDate != prebook.RequiredDate)?.RequiredDate ?? previousRevisions.FirstOrDefault()?.RequiredDate,
            MostRecentRequiredDate = mostRecent?.RequiredDate,
            PreviousComments = previousRevisions.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments) && e.Comments != prebook.Comments)?.Comments ?? 
                               previousRevisions.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments))?.Comments ?? 
                               string.Empty,
            MostRecentComments = mostRecent?.Comments
        };

        var report = new Prebook(reportPrebook, items);
        return report;
    }

    public async Task<Prebook?> CreateReport(int prebookId, int emailId)
    {
        var prebook = await prebookRepository.Detail(prebookId);
        if (prebook == null) {
            return null;
        }

        var emails = (await prebookEmailRepository.GetEmailsForPrebook(prebookId)).OrderBy(e => e.Created).ToList();
        var currentIndex = emails.FindIndex(e => e.Id == emailId);
        if (currentIndex == -1) {
            return null;
        }

        var previousRevisions = emails.Where((_, i) => i < currentIndex).OrderByDescending(e => e.Created).ToList();
        var revised = previousRevisions.Any();
        var prebookEmail = emails[currentIndex];
        var mostRecent = previousRevisions.FirstOrDefault();

        var futureOrders = await futureOrderRepository.FutureOrdersForPrebook(prebookId);

        var items = prebookEmail.Items.Select(i => {
            var prebookItem = prebook.Items.FirstOrDefault(pi => pi.Id == i.PrebookItemId);
            var previous = previousRevisions.Aggregate(new List<PrebookEmailItem>(), (list, e) => {
                list.AddRange(e.Items.Where(pi => pi.PrebookItemId == i.PrebookItemId));
                return list;
            });
            var mostRecentEmailItem = mostRecent?.Items.FirstOrDefault(pi => pi.PrebookItemId == i.PrebookItemId);

            FutureOrderDetail? futureOrder = default;
            FutureOrderDetailItem? futureOrderItem = default;
            foreach (var fo in futureOrders) {
                futureOrderItem = fo.Items.FirstOrDefault(foi => foi.Id == prebookItem?.FutureOrderItemId);
                if (futureOrderItem != null) {
                    futureOrder = fo;
                    break;
                }
            }

            var growerItemNotes = MakeGrowerItemNotes(futureOrder?.GrowerItemNotes ?? prebook.GrowerItemNotes, futureOrderItem?.GrowerItemNotes ?? prebookItem?.GrowerItemNotes);

            var sortOrder = prebook.Items
                .Where(pi =>
                    pi.Id == i.PrebookItemId ||
                    (pi.SpirePartNumber == i.SpirePartNumber && pi.FutureOrderId != futureOrder?.Id))
                .Select(pi => futureOrders.FirstOrDefault(f => f.Items.Any(fi => fi.Id == pi.FutureOrderItemId))?.Items.FirstOrDefault(fi => fi.Id == pi.FutureOrderItemId))
                .MinBy(fi => (fi?.SortOrder).GetValueOrDefault(1))?.SortOrder ?? int.MaxValue;

            return new PrebookReportPrebookItem {
                SortOrder = sortOrder,
                HasRevision = revised,
                ItemWasRevised = previousRevisions.Any(e => e.Items.Any(item => item.SpirePartNumber == i.SpirePartNumber)),
                OrderQuantity = i.OrderQuantity,
                PreviousOrderQuantity = previous.FirstOrDefault(p => p.OrderQuantity != i.OrderQuantity)?.OrderQuantity ?? previous.FirstOrDefault()?.OrderQuantity,
                MostRecentOrderQuantity = mostRecentEmailItem?.OrderQuantity,
                IsApproximate = prebookItem?.IsApproximate == true,
                PackQuantity = i.PackQuantity,
                PreviousPackQuantity = previous.FirstOrDefault(p => p.PackQuantity != i.PackQuantity)?.PackQuantity ?? previous.FirstOrDefault()?.PackQuantity,
                Description = i.Description,
                PreviousDescription = previous.FirstOrDefault(p => p.Description != i.Description)?.Description ?? previous.FirstOrDefault()?.Description ?? string.Empty,
                MostRecentDescription = mostRecentEmailItem?.Description,
                HasPotCover = !string.IsNullOrWhiteSpace(i.PotCover),
                PotCover = i.PotCover ?? string.Empty,
                PreviousHasPotCover = previous.Any(p => !string.IsNullOrWhiteSpace(p.PotCover)),
                PreviousPotCover = FindPreviousValue(previous, (p) => p?.PotCover, i.PotCover),
                MostRecentPotCover = mostRecentEmailItem?.PotCover,
                BoxCode = i.BoxCode ?? string.Empty,
                PreviousBoxCode = FindPreviousValue(previous, p => p?.BoxCode, i.BoxCode),
                MostRecentBoxCode = mostRecentEmailItem?.BoxCode,
                DateCode = i.DateCode ?? string.Empty,
                PreviousDateCode = FindPreviousValue(previous, p => p?.DateCode, i.DateCode),
                MostRecentDateCode = mostRecentEmailItem?.DateCode,
                UPC = i.Upc ?? string.Empty,
                PreviousUPC = FindPreviousValue(previous, p => p?.Upc, i.Upc),
                MostRecentUPC = mostRecentEmailItem?.Upc,
                Retail = i.Retail ?? string.Empty,
                PreviousRetail = FindPreviousValue(previous, p => p?.Retail, i.Retail),
                MostRecentRetail = mostRecentEmailItem?.Retail,
                WeightsAndMeasures = i.WeightsAndMeasures,
                Comments = i.Comments ?? string.Empty,
                PreviousComments = FindPreviousValue(previous, p => p?.Comments, i.Comments),
                MostRecentComments = mostRecentEmailItem?.Comments,
                GrowerItemNotes = growerItemNotes,
                BlanketWeekId = prebookItem?.BlanketWeekId ?? string.Empty,
                SpecialPrice = prebookItem?.SpecialPrice,
                UpgradeSheet = prebookItem?.UpgradeSheet ?? false,
                PreviousUpgradeSheet = previous.Any(p => p.UpgradeSheet)
            };
        })
        .OrderBy(i => i.SortOrder)
        .ToList();

        var reportPrebook = new PrebookReportPrebook {
            Id = prebook.Id,
            IsBlanket = prebook.IsBlanket,
            HasRevision = revised,
            RequiredDate = prebookEmail.RequiredDate,
            BlanketStartDate = prebook.BlanketStartDate,
            PreviousRevisionDate = previousRevisions.FirstOrDefault()?.Created,
            SeasonName = prebook.SeasonName ?? string.Empty,
            VendorName = prebook.VendorName ?? string.Empty,
            SalespersonName = prebook.SalespersonName ?? string.Empty,
            Comments = prebook.Comments ?? string.Empty,
            PreviousRequiredDate = previousRevisions.FirstOrDefault(e => e.RequiredDate != prebook.RequiredDate)?.RequiredDate ?? previousRevisions.FirstOrDefault()?.RequiredDate,
            PreviousComments = previousRevisions.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments) && e.Comments != prebook.Comments)?.Comments ?? 
                               previousRevisions.FirstOrDefault(e => !string.IsNullOrWhiteSpace(e.Comments))?.Comments ?? 
                               string.Empty,
            MostRecentRequiredDate = mostRecent?.RequiredDate,
            MostRecentComments = mostRecent?.Comments
        };

        var report = new Prebook(reportPrebook, items);
        return report;
    }

    public async Task<MemoryStream> OverAndAboveReport(DateTime? startDate, DateTime? endDate)
    {
        var items = await prebookRepository.OverAndAbove(startDate, endDate);

        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 1;
        var col = 1;

        sheet.Cell(row, col).SetValue("Prebooks ordered without Blankets when blankets were available");
        sheet.Cell(row, col).Style.Font.Bold = true;
        row++;

        if (startDate.HasValue && !endDate.HasValue) {
            sheet.Cell(row, col).SetValue($"Showing required dates beginning {startDate.Value:MMM d, yyyy}");
            row++;
        } else if (startDate.HasValue && endDate.HasValue) {
            sheet.Cell(row, col).SetValue($"Showing required dates between {startDate.Value:MMM d, yyyy} and {endDate.Value:MMM d, yyyy}");
            row++;
        } else if (!startDate.HasValue && endDate.HasValue) {
            sheet.Cell(row, col).SetValue($"Showing required dates until {endDate.Value:MMM d, yyyy}");
            row++;
        }

        row++;

        sheet.Cell(row, col).SetValue("Prebook Id");

        sheet.Cell(row, col).SetValue("Prebook Id");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Required Date");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Week");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Customer");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Box Code");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Vendor");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Part Number");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Description");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Quantity");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Blanket Quantity");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;
        sheet.Cell(row, col).SetValue("Ordered");
        sheet.Cell(row, col).Style.Font.Bold = true;
        col++;

        sheet.Range(row, 1, row, col).Style.Border.BottomBorder = XLBorderStyleValues.Thin;

        row++;
        foreach (var item in items) {
            col = 1;

            sheet.Cell(row, col).SetValue($"{item.PrebookId:00000}");
            col++;
            sheet.Cell(row, col).SetValue(item.RequiredDate);
            col++;
            sheet.Cell(row, col).SetValue(item.Week);
            col++;
            sheet.Cell(row, col).SetValue(item.Customer);
            col++;
            sheet.Cell(row, col).SetValue(item.BoxCode);
            col++;
            sheet.Cell(row, col).SetValue(item.Vendor);
            col++;
            sheet.Cell(row, col).SetValue(item.SpirePartNumber);
            col++;
            sheet.Cell(row, col).SetValue(item.Description);
            col++;
            sheet.Cell(row, col).SetValue(item.OrderQuantity);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
            col++;
            sheet.Cell(row, col).SetValue(item.BlanketQuantity);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
            col++;
            sheet.Cell(row, col).SetValue(item.OrderQuantity);
            sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
            col++;

            row++;
        }

        sheet.Range(row, 1, row, col).Style.Border.TopBorder = XLBorderStyleValues.Double;
        sheet.Range(row, 1, row, col).Style.Font.Bold = true;

        sheet.Columns(1, col).AdjustToContents();

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }

    private static string FindPreviousValue(List<PrebookEmailItem> previousItems,
        Func<PrebookEmailItem?, string?> selector, string? currentValue) =>
        selector(previousItems.FirstOrDefault(e => !string.IsNullOrWhiteSpace(selector(e)) && selector(e) != currentValue)) ??
        selector(previousItems.FirstOrDefault(e => !string.IsNullOrWhiteSpace(selector(e)))) ??
        string.Empty;

    private static string MakeGrowerItemNotes(string? header, string? item) =>
        (header ?? string.Empty) + (string.IsNullOrEmpty(header) || string.IsNullOrEmpty(item) ? string.Empty : "\n") + (item ?? string.Empty);
}