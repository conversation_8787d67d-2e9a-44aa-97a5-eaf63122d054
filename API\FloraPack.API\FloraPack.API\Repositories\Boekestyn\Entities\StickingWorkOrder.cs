﻿namespace FloraPack.API.Repositories.Boekestyn.Entities;

public class StickingWorkOrder
{
    public int Id { get; init; }
    public int ScheduleId { get; init; }
    public int SortOrder { get; init; }
    public string OrderId { get; init; } = string.Empty;
    public string OrderNumber { get; init; } = string.Empty;
    public string PlantSize { get; init; } = string.Empty;
    public string PlantCrop { get; init; } = string.Empty;
    public string Customer { get; init; } = string.Empty;
    public int Cuttings { get; init; }
    public int Pots { get; init; }
    public int Cases { get; init; }
    public string Zone { get; init; } = string.Empty;
    public decimal EstimatedHours { get; init; }
    public decimal CrewSize { get; init; }
    public string? OrderComments { get; init; }
    public string? StickingComments { get; init; }

    public List<StickingWorkOrderVariety> Varieties { get; init; } = [];
}