﻿using FloraPack.API.Reports;
using FloraPack.API.Reports.CustomerConfirmations;
using FloraPack.API.Reports.FutureOrders;
using FloraPack.API.Reports.Prebooks;
using FloraPack.API.Repositories.Prebooks;
using GrapeCity.ActiveReports.Export.Pdf.Section;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Reports")]
public class ReportsController(
    IConfiguration configuration,
    PrebookReportFactory prebookReportFactory,
    FutureOrderReportFactory futureOrderReportFactory,
    CustomerConfirmationFactory customerConfirmationFactory,
    PrebookRepository prebookRepository)
    : FloraPackControllerBase(configuration)
{
    [AllowAnonymous]
    [HttpGet("Prebooks/{id:int}")]
    public async Task<IActionResult> Prebook(int id)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var report = await prebookReportFactory.CreateReport(id);
        if (report == null) {
            return NotFound();
        }

        report.Run();
        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(report.Document, memStream);
        memStream.Position = 0;

        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Prebook {id:00000}.pdf" : "inline");
        return File(memStream, "application/pdf");
    }

    [AllowAnonymous]
    [HttpGet("Prebooks/Batch")]
    public async Task<IActionResult> PrebooksBatch()
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var hasValues = Request.Query.TryGetValue("ids", out var value);
        if (!hasValues) {
            return BadRequest();
        }

        var ids = value
            .Select(v => int.TryParse(v, out var id) ? id : default(int?))
            .Where(v => v.HasValue)
            .Select(v => v.GetValueOrDefault()).ToList();

        var reports = new List<Prebook>();
        foreach (var id in ids) {
            var report = await prebookReportFactory.CreateReport(id);
            if (report != null) {
                reports.Add(report);
            }
        }

        if (reports.Count == 0) {
            return NotFound();
        }

        var first = reports.First();
        first.Run();

        foreach (var report in reports.Where((_, i) => i > 0)) {
            report.Run();
            first.Document.Pages.AddRange(report.Document.Pages);
        }

        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(first.Document, memStream);
        memStream.Position = 0;

        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Prebooks.pdf" : "inline");
        return File(memStream, "application/pdf");
    }

    [AllowAnonymous]
    [HttpGet("Prebooks/{prebookId:int}/Email/{emailId:int}")]
    public async Task<IActionResult> CreateEmail(int prebookId, int emailId)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var report = await prebookReportFactory.CreateReport(prebookId, emailId);
        if (report == null) {
            return NotFound();
        }

        report.Run();
        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(report.Document, memStream);
        memStream.Position = 0;
        
        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Prebook {prebookId:00000}.pdf" : "inline");
        return File(memStream, "application/pdf");
    }

    [AllowAnonymous]
    [HttpGet("Future-Orders/{id:int}")]
    public async Task<IActionResult> FutureOrder(int id)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var report = await futureOrderReportFactory.CreateReport(id);
        if (report == null) {
            return NotFound();
        }

        report.Run();
        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(report.Document, memStream);
        memStream.Position = 0;

        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Future Order {id:00000}.pdf" : "inline");
        return File(memStream, "application/pdf");
    }

    [AllowAnonymous]
    [HttpGet("Future-Orders/{id:int}/Customer-Confirmation")]
    public async Task<IActionResult> CustomerConfirmation(int id, [FromQuery] bool includeZeroQuantity = true)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var report = await customerConfirmationFactory.CreateReport(id, includeZeroQuantity);
        if (report == null) {
            return NotFound();
        }

        report.Run();
        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(report.Document, memStream);
        memStream.Position = 0;

        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Customer Confirmation {id:00000}.pdf" : "inline");
        return File(memStream, "application/pdf");
    }

    [AllowAnonymous]
    [HttpGet("Future-Orders/{id:int}/Prebooks")]
    public async Task<IActionResult> FutureOrderPrebooks(int id)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");
        
        var prebooks = await prebookRepository.DetailsForFutureOrder(id);

        if (prebooks.Count == 0) {
            return NotFound();
        }

        var first = prebooks.First();
        var report = await prebookReportFactory.CreateReport(first.Id);
        if (report == null) {
            return NotFound();
        }

        report.Run();

        for (var i = 1; i < prebooks.Count; i++) {
            var prebook = prebooks[i];
            var r = await prebookReportFactory.CreateReport(prebook.Id);
            if (r != null) {
                r.Run();
                report.Document.Pages.AddRange(r.Document.Pages);
            }
        }

        var export = new PdfExport();
        var memStream = new MemoryStream();
        export.Export(report.Document, memStream);
        memStream.Position = 0;

        Response.Headers.Append("Content-Disposition", isDownload ? $"attachment;filename=Future Order {id:00000} Prebooks.pdf" : "inline");
        return File(memStream, "application/pdf");
    }
}