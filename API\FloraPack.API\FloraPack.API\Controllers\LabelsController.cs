﻿using FloraPack.API.Reports;
// using GrapeCity.ActiveReports.Export.Pdf.Section;
using Microsoft.AspNetCore.Mvc;

namespace FloraPack.API.Controllers;

[Route("API/Labels")]
public class LabelsController(IConfiguration configuration) : FloraPackControllerBase(configuration)
{
    [HttpPost("Coborns")]
    public IActionResult Coborns(CobornsLabelModel model)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var labelsByItem = new Dictionary<string, int>();

        var labels = model.Stores
            .Aggregate(new List<CobornsLabelItem>(), (list, store) => {
                store.Orders.ForEach(order =>
                    list.AddRange(Enumerable.Range(0, order.Quantity).Select(_ => {

                                var description = order.Description ?? string.Empty;
                                if (!labelsByItem.TryAdd(description, 1)) {
                                    labelsByItem[description]++;
                                }

                                return new CobornsLabelItem {
                                    StoreName = store.Name ?? string.Empty,
                                    StoreNumber = store.StoreNumber,
                                    Description = description,
                                    ProductNumber = order.ProductNumber ?? string.Empty,
                                    Size = order.Size ?? string.Empty,
                                    UPC = order.Upc ?? string.Empty,
                                    PurchaseOrderNumber = model.PurchaseOrderNumber ?? string.Empty,
                                    ReceivingDate = model.RequiredDate,
                                    PackQuantity = order.PackQuantity,
                                };
                            }
                        )
                    )
                );
                return list;
            })
            .OrderBy(l => l.Description)
            .ThenBy(l => l.StoreNumber)
            .ToList();

        var labelNumber = 1;
        CobornsLabelItem? previous = default;
        labels.ForEach(l => {
            if (l.Description == previous?.Description) {
                labelNumber++;
            } else {
                labelNumber = 1;
            }

            l.LabelNumber = labelNumber;

            if (labelsByItem.TryGetValue(l.Description, out var value)) {
                l.LabelCount = value;
            }

            previous = l;
        });
        
        // TODO: Replace with alternative PDF generation
        // var report = new CobornsLabels(labels);
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;

        // Response.Headers.Append("Content-Disposition", isDownload ? "attachment;filename=CobornsLabels.pdf" : "inline");
        // return File(memStream, "application/pdf");

        return BadRequest("Label generation temporarily disabled - GrapeCity ActiveReports not available");
    }

    [HttpPost("Albrechts")]
    public IActionResult Albrechts(AlbrechtsLabelModel model)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var labelsByItem = new Dictionary<string, int>();

        var labels = model.Stores
            .Aggregate(new List<AlbrechtsLabelItem>(), (list, store) => {
                store.Orders
                    .OrderBy(o => o.ProductNumber)
                    .ToList()
                    .ForEach(order =>
                        list.AddRange(Enumerable.Range(0, order.Quantity).Select(_ => {

                                var productNumber = string.IsNullOrWhiteSpace(order.ProductNumber) ? order.Description : order.ProductNumber;
                                if (!labelsByItem.TryAdd(productNumber, 1)) {
                                    labelsByItem[productNumber]++;
                                }

                                return new AlbrechtsLabelItem {
                                    StoreName = store.Name ?? string.Empty,
                                    StoreNumber = store.StoreNumber,
                                    Description = order.Description,
                                    ProductNumber = productNumber,
                                    Cost = order.Cost ?? string.Empty,
                                    Retail = order.Retail ?? string.Empty,
                                    UPC = order.Upc ?? string.Empty,
                                    PurchaseOrderNumber = model.PurchaseOrderNumber ?? string.Empty,
                                    ReceivingDate = model.RequiredDate,
                                    PackQuantity = order.PackQuantity,
                                };
                            })
                        )
                    );
                return list;
            })
            .OrderBy(l => l.ProductNumber)
            .ThenBy(l => l.Description)
            .ThenBy(l => l.StoreNumber)
            .ToList();

        var labelNumber = 1;
        AlbrechtsLabelItem? previous = default;
        labels.ForEach(l => {
            var productNumber = string.IsNullOrWhiteSpace(l.ProductNumber) ? l.Description : l.ProductNumber;
            var previousProductNumber = string.IsNullOrWhiteSpace(previous?.ProductNumber) ? previous?.Description : previous?.ProductNumber;
            if (productNumber == previousProductNumber) {
                labelNumber++;
            } else {
                labelNumber = 1;
            }

            l.LabelNumber = labelNumber;

            if (labelsByItem.TryGetValue(productNumber, out var value)) {
                l.LabelCount = value;
            }

            previous = l;
        });
        
        // TODO: Replace with alternative PDF generation
        // var report = new AlbrechtsLabels(labels);
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;

        // Response.Headers.Append("Content-Disposition", isDownload ? "attachment;filename=AlbrechtsLabels.pdf" : "inline");
        // return File(memStream, "application/pdf");

        return BadRequest("Label generation temporarily disabled - GrapeCity ActiveReports not available");
    }

    [HttpPost("Heinens")]
    public IActionResult Heinens([FromBody] HeinensLabelModel model, [FromQuery] string type)
    {
        var isDownload = Request.Query.Any(q => q.Key == "download");

        var destinations = model.Stores.Select(s => s.Destination).Distinct();


        var caseCountByItem = new Dictionary<string, int>();
        model.Stores.ForEach(store => {
            store.Orders
                .Where(o => !string.IsNullOrWhiteSpace(o.ProductNumber))
                .Select(o => o.ProductNumber)
                .ToList()
                .ForEach(p => {
                    if (p != null && !caseCountByItem.ContainsKey(p)) {
                        var caseCount = model.Stores.Sum(s => s.Orders.Where(o => o.ProductNumber == p).Sum(o => o.Quantity));
                        caseCountByItem.Add(p, caseCount);
                    }
                });
        });

        var labels = destinations
            .Aggregate(new List<HeinensLabelItem>(), (list, destination) => {
                var destinationStores = model.Stores.Where(s => s.Destination == destination).ToList();
                var destinationItems = destinationStores
                    .Aggregate(new Dictionary<string, int>(), (items, s) => {
                        s.Orders
                            .Where(o => o.Type == type)
                            .ToList()
                            .ForEach(o => {
                                var key = o.ProductNumber ?? string.Empty;
                                if (items.ContainsKey(key)) {
                                    items[key] += o.Quantity;
                                } else {
                                    items.Add(key, o.Quantity);
                                }
                            });
                        return items;
                    });

                foreach (var destinationItem in destinationItems) {
                    var labelNumber = 1;
                    var skidLabelsPrinted = false;
                    destinationStores.ForEach(store => {
                        store.Orders
                            .Where(o => o.ProductNumber == destinationItem.Key)
                            .ToList()
                            .ForEach(order => {
                                    caseCountByItem.TryGetValue(order.ProductNumber ?? string.Empty, out var caseCount);
                                    var isChicago = store.Destination == "Chicago";
                                    // we need to print box labels for the Chicago destinations and also 
                                    var useSkidLabels = !isChicago && caseCount >= 100 && order.SkidQuantity.HasValue;
                                    // there are 4 labels per skid
                                    var labelQuantity = useSkidLabels ? order.SkidQuantity!.Value * 4 : order.Quantity;
                                    // just need to add the skid labels once
                                    if (useSkidLabels && skidLabelsPrinted) {
                                        labelQuantity = 0;
                                    }

                                    list.AddRange(Enumerable.Range(0, labelQuantity).Select(_ => new HeinensLabelItem {
                                            StoreNumber = useSkidLabels
                                                ? string.Empty
                                                : (store.StoreNumber ?? string.Empty),
                                            Destination = store.Destination ?? string.Empty,
                                            Description = order.Description,
                                            ProductNumber = order.ProductNumber ?? string.Empty,
                                            UPC = order.Upc ?? string.Empty,
                                            PurchaseOrderNumber = model.PurchaseOrderNumber ?? string.Empty,
                                            ReceivingDate = model.RequiredDate,
                                            PackQuantity = order.PackQuantity,
                                            LabelCount = useSkidLabels ? 0 : destinationItem.Value,
                                            LabelNumber = useSkidLabels ? 0 : labelNumber++
                                        })
                                    );

                                    if (useSkidLabels) {
                                        skidLabelsPrinted = true;
                                    }
                                }
                            );
                    });
                }

                return list;
            })
            .OrderByDescending(l => l.Destination)
            .ThenBy(l => l.ProductNumber)
            .ToList();

        // TODO: Replace with alternative PDF generation
        // var report = new HeinensLabels(labels);
        // report.Run();
        // var export = new PdfExport();
        // var memStream = new MemoryStream();
        // export.Export(report.Document, memStream);
        // memStream.Position = 0;

        // Response.Headers.Append("Content-Disposition", isDownload ? "attachment;filename=HeinensLabels.pdf" : "inline");
        // return File(memStream, "application/pdf");

        return BadRequest("Label generation temporarily disabled - GrapeCity ActiveReports not available");
    }

    public class CobornsLabelModel
    {
        public DateTime RequiredDate { get; set; }
        public string? PurchaseOrderNumber { get; set; }
        public List<CobornsLabelStore> Stores { get; set; } = [];
    }

    public class CobornsLabelStore
    {
        public string? Name { get; set; }
        public int? StoreNumber { get; set; }
        public List<CobornsLabelStoreItem> Orders { get; set; } = [];
    }

    public class CobornsLabelStoreItem
    {
        public string? Upc { get; set; }
        public string? ProductNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Size { get; set; }
        public int? PackQuantity { get; set; }
        public int Quantity { get; set; }
    }

    public class AlbrechtsLabelModel
    {
        public DateTime RequiredDate { get; set; }
        public string? PurchaseOrderNumber { get; set; }
        public List<AlbrechtsLabelStore> Stores { get; set; } = [];
    }

    public class AlbrechtsLabelStore
    {
        public string? Name { get; set; }
        public int? StoreNumber { get; set; }
        public List<AlbrechtsLabelStoreItem> Orders { get; set; } = [];
    }

    public class AlbrechtsLabelStoreItem
    {
        public string? Upc { get; set; }
        public string? ProductNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Cost { get; set; }
        public string? Retail { get; set; }
        public int? PackQuantity { get; set; }
        public int Quantity { get; set; }
    }

    public class HeinensLabelModel
    {
        public DateTime RequiredDate { get; set; }
        public string? PurchaseOrderNumber { get; set; }
        public List<HeinensLabelStore> Stores { get; set; } = [];
    }

    public class HeinensLabelStore
    {
        public string? StoreNumber { get; set; }
        public string? Destination { get; set; }
        public List<HeinensLabelStoreItem> Orders { get; set; } = [];
    }

    public class HeinensLabelStoreItem
    {
        public string? Upc { get; set; }
        public string? ProductNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public int? PackQuantity { get; set; }
        public int Quantity { get; set; }
        public string Type { get; set; } = string.Empty;
        public int? SkidQuantity { get; set; }
    }
}