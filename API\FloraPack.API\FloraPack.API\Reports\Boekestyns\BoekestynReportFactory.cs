﻿using ClosedXML.Excel;
using FloraPack.API.Controllers;
using FloraPack.API.Repositories.Boekestyn.Entities;
using System.Globalization;

namespace FloraPack.API.Reports.Boekestyns;

public static class BoekestynReportFactory
{
    public static MemoryStream ItemList(BoekestynController.ItemListReportModel model)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var row = 3;
        var col = 1;

        sheet.Cell(row, col).SetValue("DATE");
        col++;
        sheet.Cell(row, col).SetValue("WEEK");
        col++;
        sheet.Cell(row, col).SetValue("CUSTOMER");
        col++;
        sheet.Cell(row, col).SetValue("CASES");
        col++;
        sheet.Cell(row, col).SetValue("PACK");
        col++;
        sheet.Cell(row, col).SetValue("PRODUCT");
        col++;
        sheet.Cell(row, col).SetValue("POT COVER");
        col++;
        sheet.Cell(row, col).SetValue("BOX CODE");
        col++;
        sheet.Cell(row, col).SetValue("UPC");
        col++;
        sheet.Cell(row, col).SetValue("DATE CODE");
        col++;
        sheet.Cell(row, col).SetValue("RETAIL");
        col++;
        sheet.Cell(row, col).SetValue("W&M");
        col++;
        sheet.Cell(row, col).SetValue("NOTES");
        var range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
        range.Style.Font.Bold = true;
        range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
        range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
        range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
        range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        range.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;

        row++;
        foreach (var item in model.Items) {
            col = 1;
            sheet.Cell(row, col).SetValue(item.RequiredDate);
            sheet.Cell(row, col).Style.DateFormat.SetFormat("Medium Date");
            sheet.Cell(row, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            col++;

            var parsed = DateTime.TryParse(item.RequiredDate ?? string.Empty, out var date);
            if (parsed) {
                sheet.Cell(row, col).SetValue(ISOWeek.GetWeekOfYear(date));
            }
            col++;
            var customer = (item.Customer ?? string.Empty) +
                           (string.IsNullOrWhiteSpace(item.ShipTo) ? string.Empty : $" - {item.ShipTo}");
            sheet.Cell(row, col).SetValue(customer);
            col++;
            sheet.Cell(row, col).SetValue(item.CaseCount);
            sheet.Cell(row, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            col++;
            if (item.PackQuantity.HasValue) {
                sheet.Cell(row, col).SetValue(item.PackQuantity.Value);
                sheet.Cell(row, col).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            }

            col++;
            sheet.Cell(row, col).SetValue(item.Description);
            col++;
            sheet.Cell(row, col).SetValue(item.PotCover);
            col++;
            sheet.Cell(row, col).SetValue(item.BoxCode);
            col++;
            sheet.Cell(row, col).SetValue(item.Upc);
            col++;
            sheet.Cell(row, col).SetValue(item.DateCode);
            col++;
            sheet.Cell(row, col).SetValue(item.Retail);
            col++;
            sheet.Cell(row, col).SetValue(item.WeightsAndMeasures ? "YES" : "");
            col++;

            var itemComments = item.ItemComments ?? string.Empty;
            if (!string.IsNullOrWhiteSpace(itemComments) && !string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                itemComments += " | ";
            }

            if (!string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                itemComments += item.GrowerItemNotes;
            }

            if(!string.IsNullOrWhiteSpace(item.ItemGrowerItemNotes)) {
                itemComments += $"\n{item.ItemGrowerItemNotes}";
            }
            sheet.Cell(row, col).SetValue(itemComments);

            range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
            range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
            range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
            range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
            range.Style.Border.RightBorder = XLBorderStyleValues.Thin;

            row++;
        }

        var suffix = string.Empty;
        if (model.StartDate.HasValue && !model.EndDate.HasValue) {
            suffix = $" after {model.StartDate.Value:yyyy-MM-dd}";
        } else if (model.EndDate.HasValue && !model.StartDate.HasValue) {
            suffix = $" up to {model.EndDate.Value:yyyy-MM-dd}";
        } else if (model.StartDate.HasValue && model.EndDate.HasValue) {
            suffix = $" {model.StartDate.Value:yyyy-MM-dd} - {model.EndDate.Value:yyyy-MM-dd}";
        }

        sheet.Columns(1, col).AdjustToContents();

        var title = $"Boekestyn Orders{suffix}";

        var cell = sheet.Cell(1, 1);
        cell.SetValue(title);
        cell.Style.Font.FontSize = 24D;
        cell.Style.Font.Bold = true;

        sheet.Cell(1, col).SetValue($"Generated {DateTime.Now:yyyy-MM-dd h:mm tt}");

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }

    public static MemoryStream Sales(BoekestynController.SalesReportModel model)
    {
        var book = new XLWorkbook();
        var sheet = book.AddWorksheet();

        var productionOrders = model.ProductionOrders
            .Where(o => {
                if(!string.IsNullOrWhiteSpace(model.Plant) && !string.Equals(model.Plant, o.Plant?.Id)) {
                    return false;
                }
                if(model.Customers.Count > 0 && !model.Customers.Any(c => {
                    var filterCustomer = CustomerName(c);
                    var orderCustomer = CustomerName(o.Customer?.Name);
                    return string.Equals(filterCustomer, orderCustomer);
                })) {
                    return false;
                }

                return true;
            }).ToList();

        var prebookItems = model.PrebookItems
            .Where(item => {
                if(!string.IsNullOrWhiteSpace(model.Plant) && !string.Equals(model.Plant, item.BoekestynPlantId)) {
                    return false;
                }
                if(model.Customers.Count > 0 && !model.Customers.Any(c => {
                    var filterCustomer = CustomerName(c);
                    var orderCustomer = CustomerName(item.BoekestynCustomerAbbreviation);
                    return string.Equals(filterCustomer, orderCustomer);
                })) {
                    return false;
                }
                return true;
            })
            .ToList();

        var weeks = productionOrders
            .Aggregate(new List<SalesWeek>(), (list, order) => {
                var week = WeekFromDate(order.FlowerDate);
                if (week != null) {
                    var customer = CustomerName(order.Customer?.Name);
                    var salesWeek = new SalesWeek(week, customer);
                    if (!list.Contains(salesWeek)) {
                        list.Add(salesWeek);
                    }
                }
                return list;
        });

        prebookItems
            .ForEach(item => {
                var week = new Week(item.Year, item.Week);
                var customer = CustomerName(item.BoekestynCustomerAbbreviation);
                var salesWeek = new SalesWeek(week, customer);
                if (!weeks.Contains(salesWeek)) {
                    weeks.Add(salesWeek);
                }
            });

        weeks.Sort((a, b) => {
            var compare = string.Compare(a.Week.WeekId, b.Week.WeekId, StringComparison.Ordinal);
            if(compare == 0) {
                compare = string.Compare(a.Customer, b.Customer, StringComparison.Ordinal);
            }

            return compare;
        });

        var row = 3;
        var col = 1;

        sheet.Cell(row, col).SetValue("Customer");
        col++;
        sheet.Cell(row, col).SetValue("Ship-To");

        weeks.ForEach(w => {
            col++;
            sheet.Cell(row, col).SetValue($"Week {w.Week.WeekNumber}\n{w.Customer}");
        });

        var lastCol = col;

        var range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
        range.Style.Font.Bold = true;
        range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
        range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
        range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
        range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        range.Style.Alignment.WrapText = true;
        sheet.Columns(3, col).AdjustToContents();
        sheet.Rows(row, row).AdjustToContents();

        row++;

        col = 1;
        sheet.Cell(row, col).SetValue("Production");
        // ship-to
        col++;
        weeks.ForEach(w => {
            col++;
            var quantity = productionOrders
                .Where(o => {
                    var week = WeekFromDate(o.FlowerDate);
                    if(week == null) return false;
                    return new SalesWeek(week, CustomerName(o.Customer?.Name)) == w;
                })
                .Sum(o => o.Cases);
            sheet.Cell(row, col).SetValue(quantity);
        });

        range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
        range.Style.Font.Bold = true;
        range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
        range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
        range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
        range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        range.Style.Fill.BackgroundColor = XLColor.LightGray;
        range.Style.NumberFormat.SetFormat("#,##0");

        var singlePrebookItems = prebookItems.Where(i => !i.MultipleProducts || i.IgnoreOverrideQuantity).ToList();
        var multiplePrebookItems = prebookItems.Where(i => i.MultipleProducts && !i.IgnoreOverrideQuantity).ToList();

        var salesCustomers = singlePrebookItems
                .Aggregate(new List<CustomerAndShipTo>(), (list, item) => {
                    var customer = new CustomerAndShipTo(item.CustomerName, item.ShipToName ?? item.BoxCode ?? string.Empty);
                    
                    if (!list.Contains(customer)) {
                        list.Add(customer);
                    }
                    return list;
                })
                .OrderBy(c => c.CustomerName)
                .ThenBy(c => c.ShipTo)
                .ToList();

        salesCustomers.ForEach(customer => {
            row++;
            col = 1;
            
            var customerPrebookItems = singlePrebookItems
                .Where(i => i.CustomerName == customer.CustomerName && i.ShipToName == customer.ShipTo)
                .ToList();

            sheet.Cell(row, col).SetValue(customer.CustomerName);

            col++;
            sheet.Cell(row, col).SetValue(customer.ShipTo);

            weeks.ForEach(w => {
                col++;
                var quantity = customerPrebookItems
                    .Where(o => {
                        var week = new Week(o.Year, o.Week);
                        return new SalesWeek(week, CustomerName(o.BoekestynCustomerAbbreviation)) == w;
                    })
                    .Sum(i => BoekCaseQuantity(i, model.Plants));
                sheet.Cell(row, col).SetValue(quantity);
                sheet.Cell(row, col).Style.NumberFormat.SetFormat("#,##0");
            });
        });

        row++;

        col = 1;
        sheet.Cell(row, col).SetValue("Sales");
        // ship-to
        col++;
        weeks.ForEach(w => {
            col++;
            var quantity = singlePrebookItems
                .Where(o => {
                    var week = new Week(o.Year, o.Week);
                    return new SalesWeek(week, CustomerName(o.BoekestynCustomerAbbreviation)) == w;
                })
                .Sum(i => BoekCaseQuantity(i, model.Plants));
            sheet.Cell(row, col).SetValue(quantity);
        });

        range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
        range.Style.Font.Bold = true;
        range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
        range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
        range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
        range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        range.Style.Fill.BackgroundColor = XLColor.LightGray;
        range.Style.NumberFormat.SetFormat("#,##0");

        if (multiplePrebookItems.Any()) {
            row++;

            col = 1;
            sheet.Cell(row, col).SetValue("Sales (Blooming Assorted)");
            // ship-to
            col++;
            weeks.ForEach(w => {
                col++;
                var quantity = multiplePrebookItems
                    .Where(o => {
                        var week = new Week(o.Year, o.Week);
                        return new SalesWeek(week, CustomerName(o.BoekestynCustomerAbbreviation)) == w;
                    })
                    .Sum(i => BoekCaseQuantity(i, model.Plants));
                sheet.Cell(row, col).SetValue(quantity);
            });

            range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
            range.Style.Font.Bold = true;
            range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
            range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
            range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
            range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
            range.Style.Fill.BackgroundColor = XLColor.LightGray;
            range.Style.NumberFormat.SetFormat("#,##0");
        }

        row++;

        col = 1;
        sheet.Cell(row, col).SetValue("Available");
        // ship-to
        col++;
        weeks.ForEach(w => {
            col++;
            var cell = sheet.Cell(row, col);
            var formula = "=R4C-R[-1]C";
            if (multiplePrebookItems.Any()) {
                formula += "-R[-2]C";
            }
            cell.SetFormulaR1C1(formula);
            if (cell.TryGetValue(out int quantity) && quantity < 0) {
                cell.Style.Font.FontColor = XLColor.Red;
            }
        });

        range = sheet.Range(sheet.Cell(row, 1), sheet.Cell(row, col));
        range.Style.Font.Bold = true;
        range.Style.Border.BottomBorder = XLBorderStyleValues.Thin;
        range.Style.Border.LeftBorder = XLBorderStyleValues.Thin;
        range.Style.Border.TopBorder = XLBorderStyleValues.Thin;
        range.Style.Border.RightBorder = XLBorderStyleValues.Thin;
        range.Style.Fill.BackgroundColor = XLColor.LightGray;
        range.Style.NumberFormat.SetFormat("#,##0");

        sheet.Columns(1, lastCol).AdjustToContents();

        sheet.Cell(1, lastCol).SetValue($"Generated {DateTime.Now:yyyy-MM-dd h:mm tt}");
        sheet.Cell(1, lastCol).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

        var stream = new MemoryStream();
        book.SaveAs(stream);
        stream.Position = 0;
        return stream;
    }

    private record SalesWeek(Week Week, string Customer);
    private record Week(int Year, int WeekNumber)
    {
        public string WeekId => $"{Year:0000}{WeekNumber:00}";
    }

    private static Week? WeekFromDate(string? date)
    {
        if(date != null && DateTime.TryParse(date, out var parsed)) {
            var year = ISOWeek.GetYear(parsed);
            var week = ISOWeek.GetWeekOfYear(parsed);
            return new Week(year, week);
        }
    
        return null;
    }

    private static string CustomerName(string? rawName) =>
        string.IsNullOrWhiteSpace(rawName) ? "Wk" : 
            string.Equals(rawName, "Weekly", StringComparison.InvariantCultureIgnoreCase) ? "Wk" : rawName;

    private static int BoekCaseQuantity(BoekestynPrebookItem item, List<Plant> plants)
    {
        var  caseQuantity = plants.FirstOrDefault(p => p.Id == item.BoekestynPlantId)?.PotsPerCase ?? 1;
        var pots = item.OrderQuantity * item.PackQuantity;
        var quantityPerFinishedItem = item.QuantityPerFinishedItem ?? 1;
        var divisor = item.MultipleProducts && item.PackQuantity > 0 && !item.IgnoreOverrideQuantity ? item.PackQuantity : 1;
        if(divisor == 0) {
            divisor = 1;
        }
        var cases = Convert.ToInt32(System.Math.Ceiling(((double)pots / caseQuantity) * quantityPerFinishedItem / divisor));
        return cases;
    }

    private record CustomerAndShipTo(string CustomerName, string ShipTo);
}