using System;
using System.Collections.Generic;
using System.Web;
using DocumentFormat.OpenXml.Office2010.Excel;
using GrapeCity.ActiveReports.Document.Section;

namespace FloraPack.API.Reports
{
    public partial class PrebookItem : GrapeCity.ActiveReports.SectionReport
    {
        private int _index;

        public PrebookItem()
        {
            InitializeComponent();
        }

        private void OnBeforePrint(object sender, EventArgs e)
        {
            var bottom = Math.Max(txtQtyBox.Height,
                Math.Max(rtfDescription.Height,
                    Math.Max(rtfPackQuantity.Height,
                        Math.Max(rtfPotCover.Height,
                            Math.Max(rtfUPC.Height,
                                Math.Max(rtfDateCode.Height,
                                    Math.Max(rtfRetail.Height,
                                        Math.Max(txtWeightsAndMeasuresBox.Height, rtfBoxCode.Height))))))));

            txtQtyBox.Height = bottom;
            rtfDescription.Height = bottom;
            rtfPackQuantity.Height = bottom;
            rtfPotCover.Height = bottom;
            rtfUPC.Height = bottom;
            rtfDateCode.Height = bottom;
            rtfRetail.Height = bottom;
            txtWeightsAndMeasuresBox.Height = bottom;
            rtfBoxCode.Height = bottom;
            detail.Height = bottom;
        }

        private void OnFormat(object sender, EventArgs e)
        {
            System.Drawing.Color highlightColour = System.Drawing.Color.Yellow;

            if (DataSource is List<PrebookReportPrebookItem> items && _index < items.Count) {
                var item = items[_index];

                txtOrderQuantity.Alignment = TextAlignment.Left;
                txtOrderQuantity.Width = txtOrderQuantity.Width;
                txtOrderQuantity.Left = txtOrderQuantity.Width;
                txtPreviousQuantity.Visible = true;

                if (item.IsNew || (!item.WasChanged && !item.WasRemoved)) {
                    txtOrderQuantity.Alignment = TextAlignment.Center;
                    txtOrderQuantity.Width = txtOrderQuantity.Width * 2;
                    txtOrderQuantity.Left = 0;
                    txtPreviousQuantity.Visible = false;

                    if (item.IsNew) {
                        // it doesn't all fit sometimes
                        var prefix = item.IsApproximate ? "*" : "**";
                        var suffix = item.IsApproximate ? string.Empty : "**";
                        txtOrderQuantity.Value = $"{prefix}{item.OrderQuantityDisplay}{suffix}";
                    }
                }

                if (item.MostRecentOrderQuantity.HasValue && item.MostRecentOrderQuantity.Value != item.OrderQuantity) {
                    txtOrderQuantity.BackColor = highlightColour;
                } else {
                    txtOrderQuantity.BackColor = System.Drawing.Color.Transparent;
                }

                var html = "<html><body style='margin: 0; padding: 0 3px;'><div style='text-align: center; font-family: Calibri; font-size: 9pt;'>{0}</div></body></html>";
                var addedHtml = "<html><body style='margin: 0; padding: 0 3px;'><div style='text-align: center; font-family: Calibri; font-size: 8pt; font-weight: bold; background-color: #ffff00'>**{0}**</div></body></html>";
                var changedHtml = "<html><body style='margin: 0; padding: 0 3px 10px 3px;'><div style='text-align: center; font-family: Calibri; font-size: 9pt; text-decoration: line-through;'>{1}</div><div style='text-align: center; font-family: Calibri; font-size: 9pt; {2}'>{0}</div></body></html>";


                var packChanged = item.PreviousPackQuantity.HasValue && item.PackQuantity.HasValue && item.PreviousPackQuantity.Value != item.PackQuantity.Value;
                if (packChanged) {
                    var packQuantityBackground = BackgroundColour(item.MostRecentPackQuantity.HasValue &&  item.PackQuantity.HasValue && item.MostRecentPackQuantity.Value != item.PackQuantity.Value);
                    rtfPackQuantity.Html = string.Format(changedHtml, item.PackQuantity, item.PreviousPackQuantity, packQuantityBackground);
                } else {
                    rtfPackQuantity.Html = string.Format(html, item.PackQuantity);
                }

                var descriptionHtml = "<html><body style='margin: 0; padding: 0 3px 10px 0;'>";
                var descriptionChanged = !string.IsNullOrWhiteSpace(item.PreviousDescription) && !string.IsNullOrWhiteSpace(item.Description) && item.PreviousDescription != item.Description;
                if (descriptionChanged) {
                    descriptionHtml += $"<div style='font-family: Calibri; font-size: 9pt; text-decoration: line-through;'>{item.PreviousDescription}</div>";
                }

                var descriptionBackground = BackgroundColour(item.MostRecentDescription != null && item.MostRecentDescription != item.Description);
                descriptionHtml += $"<div style='font-family: Calibri; font-size: 9pt; {descriptionBackground}'>{item.Description}</div>";
                var comments = HttpUtility.HtmlEncode(item.Comments).Replace("\n", "<br>");
                if (!string.IsNullOrWhiteSpace(item.Comments)) {
                    if (item.HasRevision && string.IsNullOrWhiteSpace(item.PreviousComments)) {
                        comments = $"<span style='font-weight: bold; background-color: #ffff00'>**{comments}**</span>";
                    } else if (!string.IsNullOrWhiteSpace(item.PreviousComments) && item.Comments != item.PreviousComments) {
                        var previousComments =  HttpUtility.HtmlEncode(item.PreviousComments).Replace("\n", "<br>");
                        var commentsBackground = BackgroundColour(item.MostRecentComments != null && item.Comments != item.MostRecentComments);
                        comments = $"<span style='text-decoration: line-through; {commentsBackground}'>{previousComments}</span><br><span>{comments}</span>";
                    }
                }
                if (!string.IsNullOrWhiteSpace(item.Comments) && !string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                    comments += "<br>";
                }

                if (!string.IsNullOrWhiteSpace(item.GrowerItemNotes)) {
                    comments += HttpUtility.HtmlEncode(item.GrowerItemNotes).Replace("\n", "<br>");
                }
                if (!string.IsNullOrWhiteSpace(comments)) {
                    descriptionHtml += $"<div style='margin-left: 10px; font-family: Calibri; font-size: 8pt; font-style: italic; white-space: wrap; overflow-wrap: break-word;'>{comments}</div>";
                }

                var weekId = item.WeekId;
                if (!string.IsNullOrWhiteSpace(weekId)) {
                    descriptionHtml += $"<div style='font-family: Calibri; font-size: 9pt; font-style: italic;'>{weekId}</div>";
                }

                if (item.SpecialPrice.HasValue) {
                    var specialPrice = item.SpecialPrice.Value.ToString("C2");
                    descriptionHtml += $"<div style='font-family: Calibri; font-size: 9pt; font-style: italic;'><span style='background-color: yellow;'>Promo Price: {specialPrice}</span></div>";
                }

                descriptionHtml += "</body></html>";
                rtfDescription.Html = descriptionHtml;

                var potCoverChanged = item.HasPotCover && item.PreviousHasPotCover && !string.IsNullOrWhiteSpace(item.PreviousPotCover) && !string.IsNullOrWhiteSpace(item.PotCover) && item.PreviousPotCover != item.PotCover;
                var potCoverAdded = item.HasRevision && item.HasPotCover && string.IsNullOrWhiteSpace(item.PreviousPotCover) && !item.PreviousHasPotCover && !string.IsNullOrWhiteSpace(item.PotCover);
                if (potCoverChanged) {
                    var potCoverBackground = BackgroundColour(item.MostRecentPotCover != null && item.MostRecentPotCover != item.PotCover);
                    rtfPotCover.Html = string.Format(changedHtml, item.PotCover, item.PreviousPotCover, potCoverBackground);
                } else if (potCoverAdded) {
                    rtfPotCover.Html = string.Format(addedHtml, item.PotCover);
                } else {
                    var potCover = item.HasPotCover ? item.PotCover : "";
                    rtfPotCover.Html = string.Format(html, potCover);
                }

                var boxCodeChanged = !string.IsNullOrWhiteSpace(item.PreviousBoxCode) && !string.IsNullOrWhiteSpace(item.BoxCode) && item.PreviousBoxCode != item.BoxCode;
                var boxCodeAdded = item.HasRevision && string.IsNullOrWhiteSpace(item.PreviousBoxCode) && !string.IsNullOrWhiteSpace(item.BoxCode);
                if (boxCodeChanged) {
                    var boxCodeBackground = BackgroundColour(item.MostRecentBoxCode != null && item.BoxCode != item.MostRecentBoxCode);
                    rtfBoxCode.Html = string.Format(changedHtml, item.BoxCode, item.PreviousBoxCode, boxCodeBackground);
                } else if (boxCodeAdded) {
                    rtfBoxCode.Html = string.Format(addedHtml, item.BoxCode);
                } else {
                    rtfBoxCode.Html = string.Format(html, item.BoxCode);
                }

                var dateCodeChanged = !string.IsNullOrWhiteSpace(item.PreviousDateCode) && !string.IsNullOrWhiteSpace(item.DateCode) && item.PreviousDateCode != item.DateCode;
                var dateCodeAdded = item.HasRevision && string.IsNullOrWhiteSpace(item.PreviousDateCode) && !string.IsNullOrWhiteSpace(item.DateCode);
                if (dateCodeChanged) {
                    var dateCodeBackground = BackgroundColour(item.MostRecentDateCode != null && item.DateCode != item.MostRecentDateCode);
                    rtfDateCode.Html = string.Format(changedHtml, item.DateCode, item.PreviousDateCode, dateCodeBackground);
                } else if(dateCodeAdded) {
                    rtfDateCode.Html = string.Format(addedHtml, item.DateCode);
                } else {
                    rtfDateCode.Html = string.Format(html, item.DateCode);
                }

                var upcChanged = !string.IsNullOrWhiteSpace(item.PreviousUPC) && !string.IsNullOrWhiteSpace(item.UPC) && item.PreviousUPC != item.UPC;
                var upcAdded = item.HasRevision && string.IsNullOrWhiteSpace(item.PreviousUPC) && !string.IsNullOrWhiteSpace(item.UPC);
                if (upcChanged) {
                    var upcBackground = BackgroundColour(item.MostRecentUPC != null && item.UPC != item.MostRecentUPC);
                    rtfUPC.Html = string.Format(changedHtml, item.UPC, item.PreviousUPC, upcBackground);
                } else if(upcAdded) {
                    rtfUPC.Html = string.Format(addedHtml, item.UPC);
                } else {
                    rtfUPC.Html = string.Format(html, item.UPC);
                }

                var retailChanged = !string.IsNullOrWhiteSpace(item.PreviousRetail) && !string.IsNullOrWhiteSpace(item.Retail) && item.PreviousRetail != item.Retail;
                var retailAdded = item.HasRevision && string.IsNullOrWhiteSpace(item.PreviousRetail) && !string.IsNullOrWhiteSpace(item.Retail);
                if (retailChanged) {
                    var retailBackground = BackgroundColour(item.MostRecentRetail != null && item.Retail != item.MostRecentRetail);
                    rtfRetail.Html = string.Format(changedHtml, item.Retail, item.PreviousRetail, retailBackground);
                } else if(retailAdded) {
                    rtfRetail.Html = string.Format(addedHtml, item.Retail);
                } else {
                    rtfRetail.Html = string.Format(html, item.Retail);
                }

                _index++;
            }
        }

        private static string BackgroundColour(bool changed) => changed ? "background-color: #ffff00;" : "";
    }

    public class PrebookReportPrebookItem
    {
        public int SortOrder { get; set; }
        public bool HasRevision { get; set; }
        public bool ItemWasRevised { get; set; }
        public int OrderQuantity { get; set; }
        public int? PreviousOrderQuantity { get; set; }
        public int? MostRecentOrderQuantity { get; set; }
        public int? PackQuantity { get; set; }
        public int? PreviousPackQuantity { get; set; }
        public int? MostRecentPackQuantity { get; set; }
        public string Description { get; set; } = string.Empty;
        public string PreviousDescription { get; set; } = string.Empty;
        public string? MostRecentDescription { get; set; }
        public bool HasPotCover { get; set; }
        public string PotCover { get; set; } = string.Empty;
        public bool PreviousHasPotCover { get; set; }
        public string PreviousPotCover { get; set; } = string.Empty;
        public string? MostRecentPotCover { get; set; }
        public string BoxCode { get; set; } = string.Empty;
        public string PreviousBoxCode { get; set; } = string.Empty;
        public string? MostRecentBoxCode { get; set; }
        public string DateCode { get; set; } = string.Empty;
        public string PreviousDateCode { get; set; } = string.Empty;
        public string? MostRecentDateCode { get; set; }
        public string UPC { get; set; } = string.Empty;
        public string PreviousUPC { get; set; } = string.Empty;
        public string? MostRecentUPC { get; set; }
        public string Retail { get; set; } = string.Empty;
        public string PreviousRetail { get; set; } = string.Empty;
        public string? MostRecentRetail { get; set; }
        public bool WeightsAndMeasures { get; set; }
        public string Comments { get; set; } = string.Empty;
        public string GrowerItemNotes { get; set; } = string.Empty;
        public string PreviousComments { get; set; } = string.Empty;
        public string? MostRecentComments { get; set; }
        public bool IsApproximate { get; set; }
        public string BlanketWeekId { get; set; } = string.Empty;
        public decimal? SpecialPrice { get; set; }
        public bool UpgradeSheet { get; set; }
        public bool PreviousUpgradeSheet { get; set; }
        public bool? MostRecentUpgradeSheet { get; set; }

        public string WeekId
        {
            get
            {
                if (BlanketWeekId.Length == 6 && int.TryParse(BlanketWeekId.Substring(4, 2), out var week)) {
                    return $"Week {week}";
                }

                return string.Empty;
            }
        }

        public string OrderQuantityDisplay =>
            OrderQuantity.ToString("N0") + ((IsApproximate && OrderQuantity > 0) ? " +/-" : "");

        public bool IsNew => HasRevision && !PreviousOrderQuantity.HasValue;
        public bool WasRemoved => PreviousOrderQuantity.HasValue && PreviousOrderQuantity.Value != 0 && OrderQuantity == 0;
        public bool WasChanged => PreviousOrderQuantity.HasValue && PreviousOrderQuantity.Value != OrderQuantity;
    }
}
