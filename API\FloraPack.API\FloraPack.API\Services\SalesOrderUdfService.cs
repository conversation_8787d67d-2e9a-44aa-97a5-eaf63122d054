﻿using FloraPack.API.Spire;

namespace FloraPack.API.Services;

public class SalesOrderUdfService(ILogger<SalesOrderUdfService> logger, SpireRepository spireRepository) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested) {
            try {
                logger.LogDebug("Starting {Class}.{Method}.", nameof(SalesOrderUdfService), nameof(ExecuteAsync));

                var recordCount = await spireRepository.UpdateSaleOrderUdfs();
                logger.LogInformation("Updated {Records} Sales Order UDF records.", recordCount);

                logger.LogDebug("{Class}.{Method} Complete.", nameof(SalesOrderUdfService), nameof(ExecuteAsync));

                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);

            } catch (Exception ex) {
                logger.LogError(ex, "An error occurred in {Class}.{Method}: {Error}.", nameof(SalesOrderUdfService), nameof(ExecuteAsync), ex.GetBaseException().Message);
            }
        }
    }
}