﻿using Dapper;
using System.Data;
using System.Globalization;
using FloraPack.API.Repositories.Boekestyn.Entities;

namespace FloraPack.API.Repositories.Boekestyn;

public class BoekestynRepository(IConfiguration configuration) : RepositoryBase(configuration)
{
    public async Task<IEnumerable<ItemListItem>> BoekestynItems(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();
        parameters.Add("required_start_date", startDate, DbType.Date);
        parameters.Add("required_end_date", endDate, DbType.Date);

        var items = (await GetConnection().QueryAsync<ItemListItem>("SELECT * FROM boekestyn_item_list(@required_start_date, @required_end_date)", parameters)).ToList();

        return items;
    }

    public async Task<IEnumerable<ItemListRequirementsItem>> ItemListRequirements(DateTime? startDate, DateTime? endDate)
    {
        var items = await GetConnection()
            .QueryAsync<ItemListRequirementsItem>(@"SELECT
                  i.id,
                  i.prebook_id,
                  CASE 
                    WHEN COALESCE(bp.count, 0) > 0 THEN
                      CASE WHEN COALESCE(d.ignore_override_quantity, FALSE) = TRUE THEN 'Planter' ELSE coalesce(bp.size || ' ', '') || 'Blooming Assorted' END
                    ELSE coalesce(pla.name, case when length(i.boekestyn_plant_id) > 0 then i.boekestyn_plant_id else null end, i.description, 'Unknown') || 
                      case when i.boekestyn_customer_abbreviation = 'Wk' then '' else coalesce(' ' || i.boekestyn_customer_abbreviation, '') end
                  END AS product,
                  to_char(p.required_date, 'YYYY-MM-DD') AS required_date,
                  coalesce(p.box_code, '') box_code,
                  coalesce(i.pot_cover, '') pot_cover,
                  coalesce(i.comments, '') comments,
                  i.order_quantity
                FROM
                  prebooks p
                  JOIN prebook_items i ON p.id = i.prebook_id
                  LEFT JOIN (
                    SELECT prebook_item_id, boekestyn_customer_abbreviation, coalesce(size, '') size, COUNT(*) AS count
                    FROM prebook_item_boekestyn_products LEFT JOIN boekestyn_plants ON boekestyn_plant_id = boekestyn_plants.id
                    GROUP BY prebook_item_id, boekestyn_customer_abbreviation, size
                  ) bp ON i.id = bp.prebook_item_id
                  LEFT JOIN product_defaults d ON i.spire_inventory_id = d.spire_inventory_id
                  LEFT JOIN boekestyn_plants pla ON i.boekestyn_plant_id = pla.id
                WHERE
                  p.required_date BETWEEN @start_date AND @end_date
                  AND p.deleted IS NULL
                  AND COALESCE(i.spire_part_number, '') <> ''
                  AND p.vendor_id = 143
                ORDER BY product, p.required_date",
                new { start_date = startDate, end_date = endDate });
        
        return items.ToList();
    }

    public async Task<List<BoekestynPrebookItem>> GetPrebookItems(DateTime? startDate, DateTime? endDate)
    {
        var parameters = new DynamicParameters();

        var startOfWeek = default(DateTime?);
        if (startDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(startDate.Value);
            var year = ISOWeek.GetYear(startDate.Value);
            startOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Monday);
        }

        var endOfWeek = default(DateTime?);
        if (endDate.HasValue) {
            var week = ISOWeek.GetWeekOfYear(endDate.Value);
            var year = ISOWeek.GetYear(endDate.Value);
            endOfWeek = ISOWeek.ToDateTime(year, week, DayOfWeek.Saturday);
        }

        parameters.Add("required_start_date", startOfWeek, DbType.Date);
        parameters.Add("required_end_date", endOfWeek, DbType.Date);

        var prebookItems = await GetConnection()
            .QueryAsync<BoekestynPrebookItem>(@"SELECT * FROM boekestyn_prebook_items(@required_start_date, @required_end_date);",
                parameters);

        return prebookItems.ToList();
    }
}

public class ItemListRequirementsItem
{
    public int Id { get; set; }
    public int PrebookId { get; set; }
    public string Product { get; set; } = string.Empty;
    public string RequiredDate { get; set; } = string.Empty;
    public string BoxCode { get; set; } = string.Empty;
    public string PotCover { get; set; } = string.Empty;
    public string Comments { get; set; } = string.Empty;
    public int OrderQuantity { get; set; }
}