﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-FloraPack.API-5F8C90D5-99A2-4DAF-B281-D61BB68F3863</UserSecretsId>
    <AssemblyVersion>1.0.24</AssemblyVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Core.Document.Drawing.Gdi" Version="4.3.2" /> -->
    <PackageReference Include="Lamar.Microsoft.DependencyInjection" Version="14.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.16" NoWarn="NU1605" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.16" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.9.3" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="3.9.3" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.9.3" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Services\" />
    <Folder Include="Properties\PublishProfiles\" />
    <Folder Include="Reports\Labels\" />
    <Folder Include="Repositories\Security\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Reports\Prebook.cs" Link="Reports\Prebooks\Prebook.cs" />
    <Compile Include="..\Reports\Prebook.designer.cs" Link="Reports\Prebooks\Prebook.designer.cs">
      <DependentUpon>Reports\Prebook.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\Prebook.resx" Link="Reports\Prebooks\Prebook.resx" />
    <Compile Include="..\Reports\PrebookItem.cs" Link="Reports\Prebooks\PrebookItem.cs" />
    <Compile Include="..\Reports\PrebookItem.designer.cs" Link="Reports\Prebooks\PrebookItem.designer.cs">
      <DependentUpon>Reports\PrebookItem.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\PrebookItem.resx" Link="Reports\Prebooks\PrebookItem.resx" />
    <Compile Include="..\Reports\CobornsLabels.cs" Link="Reports\Labels\CobornsLabels.cs" />
    <Compile Include="..\Reports\CobornsLabels.designer.cs" Link="Reports\Labels\CobornsLabels.designer.cs">
      <DependentUpon>Reports\CobornsLabels.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\CobornsLabels.resx" Link="Reports\Labels\CobornsLabels.resx" />
    <Compile Include="..\Reports\AlbrechtsLabels.cs" Link="Reports\Labels\AlbrechtsLabels.cs" />
    <Compile Include="..\Reports\AlbrechtsLabels.designer.cs" Link="Reports\Labels\AlbrechtsLabels.designer.cs">
      <DependentUpon>Reports\AlbrechtsLabels.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\AlbrechtsLabels.resx" Link="Reports\Labels\AlbrechtsLabels.resx" />
    <Compile Include="..\Reports\HeinensLabels.cs" Link="Reports\Labels\HeinensLabels.cs" />
    <Compile Include="..\Reports\HeinensLabels.designer.cs" Link="Reports\Labels\HeinensLabels.designer.cs">
      <DependentUpon>Reports\HeinensLabels.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\HeinensLabels.resx" Link="Reports\Labels\HeinensLabels.resx" />
    <Compile Include="..\Reports\FutureOrderSummary.cs" Link="Reports\FutureOrders\FutureOrderSummary.cs" />
    <Compile Include="..\Reports\FutureOrderSummary.designer.cs" Link="Reports\FutureOrders\FutureOrderSummary.designer.cs">
      <DependentUpon>Reports\FutureOrderSummary.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\FutureOrderSummary.resx" Link="Reports\FutureOrders\FutureOrderSummary.resx" />
    <Compile Include="..\Reports\FutureOrderSummaryItem.cs" Link="Reports\FutureOrders\FutureOrderSummaryItem.cs" />
    <Compile Include="..\Reports\FutureOrderSummaryItem.designer.cs" Link="Reports\FutureOrders\FutureOrderSummaryItem.designer.cs">
      <DependentUpon>Reports\FutureOrderSummaryItem.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\FutureOrderSummaryItem.resx" Link="Reports\FutureOrders\FutureOrderSummaryItem.resx" />
    <Compile Include="..\Reports\UpgradeSheet.cs" Link="Reports\FutureOrders\UpgradeSheet.cs" />
    <Compile Include="..\Reports\UpgradeSheet.designer.cs" Link="Reports\FutureOrders\UpgradeSheet.designer.cs">
      <DependentUpon>Reports\UpgradeSheet.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\UpgradeSheet.resx" Link="Reports\FutureOrders\UpgradeSheet.resx" />
    <Compile Include="..\Reports\CustomerConfirmation.cs" Link="Reports\CustomerConfirmations\CustomerConfirmation.cs" />
    <Compile Include="..\Reports\CustomerConfirmation.designer.cs" Link="Reports\CustomerConfirmations\CustomerConfirmation.designer.cs">
	    <DependentUpon>Reports\CustomerConfirmation.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\CustomerConfirmation.resx" Link="Reports\CustomerConfirmations\CustomerConfirmation.resx" />
    <Compile Include="..\Reports\CustomerConfirmationItem.cs" Link="Reports\CustomerConfirmations\CustomerConfirmationItem.cs" />
    <Compile Include="..\Reports\CustomerConfirmationItem.designer.cs" Link="Reports\CustomerConfirmations\CustomerConfirmationItem.designer.cs">
	    <DependentUpon>Reports\CustomerConfirmationItem.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="..\Reports\CustomerConfirmationItem.resx" Link="Reports\CustomerConfirmations\CustomerConfirmationItem.resx" />
  </ItemGroup>
  <ItemGroup>
    <!-- <PackageReference Include="GrapeCity.ActiveReports" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Chart" Version="17.1.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Chart.Win" Version="17.1.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Image" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Excel" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.SpreadBuilder" Version="2.0.0" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Word" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Html" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Xml" Version="17.2.2" /> -->
    <!-- <PackageReference Include="GrapeCity.ActiveReports.Export.Pdf" Version="17.2.2" /> -->
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>
</Project>